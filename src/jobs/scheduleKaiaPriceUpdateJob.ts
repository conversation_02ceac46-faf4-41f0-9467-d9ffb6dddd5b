// src/jobs/scheduleKaiaPriceUpdateJob.ts
import { kaiaPriceUpdateQueue } from "./bullmqConfig";
import dotenv from "dotenv";

dotenv.config();

/**
 * 调度Kaia价格更新任务
 * 
 * 该函数负责创建一个定时任务，定期从Kaiascan API获取Kaia价格
 * 并更新IapProduct表中所有产品的priceKaia字段
 * 默认每30分钟执行一次，可通过环境变量配置
 */
export async function scheduleKaiaPriceUpdateJob() {
  console.log('[ScheduleKaiaPriceUpdate] 开始设置Kaia价格更新任务定时调度...');
  
  // 默认每30分钟执行一次，可通过环境变量配置
  // CRON表达式格式：秒 分 时 日 月 星期
  // "0 */6 * * * *" 表示每6分钟执行一次
  // "0 */6 * * * *" 表示每6分钟执行一次（用于测试）
  const cronPattern = process.env.KAIA_PRICE_UPDATE_SCHEDULE || "0 */6 * * * *";
  const schedulerId = `scheduler-kaia-price-update`;

  console.log(`[ScheduleKaiaPriceUpdate] 正在设置任务: ${schedulerId}, CRON: ${cronPattern}`);
  
  try {
    await kaiaPriceUpdateQueue.upsertJobScheduler(
      schedulerId,
      {
        pattern: cronPattern,
        immediately: false, // 启动时立即执行一次
      },
      {
        name: `kaia-price-update-job`,
        data: {
          timestamp: new Date().toISOString(),
          type: 'kaia-price-update',
          description: 'Update Kaia prices for all IapProducts'
        },
        opts: {
          backoff: {
            type: 'exponential',
            delay: 5000, // 5秒后重试
          },
          attempts: 3, // 最多重试3次
          removeOnFail: 100, // 保留最近100个失败的任务
          removeOnComplete: 100, // 保留最近100个完成的任务
        },
      }
    );
    console.log(`[ScheduleKaiaPriceUpdate] Kaia价格更新任务设置成功: ${schedulerId}`);
    console.log(`[ScheduleKaiaPriceUpdate] 任务将按照以下计划执行: ${cronPattern}`);
  } catch (error) {
    console.error(`[ScheduleKaiaPriceUpdate] 设置Kaia价格更新任务失败: ${schedulerId}`, error);
    throw error;
  }
}

/**
 * 移除Kaia价格更新任务调度
 */
export async function removeKaiaPriceUpdateJob() {
  console.log('[RemoveKaiaPriceUpdate] 开始移除Kaia价格更新任务调度...');
  
  const schedulerId = `scheduler-kaia-price-update`;
  
  try {
    const result = await kaiaPriceUpdateQueue.removeJobScheduler(schedulerId);
    console.log(`[RemoveKaiaPriceUpdate] 移除任务结果: ${result ? "成功" : "任务不存在"}`);
    return result;
  } catch (error) {
    console.error(`[RemoveKaiaPriceUpdate] 移除Kaia价格更新任务失败: ${schedulerId}`, error);
    throw error;
  }
}