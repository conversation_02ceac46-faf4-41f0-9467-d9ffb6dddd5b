import { Request, Response } from 'express';
import { MyRequest } from '../types/customRequest';
import testResetService from '../services/testResetService';
import { successResponse, errorResponse } from '../utils/responseUtil';
import { tFromRequest } from '../i18n';
import { logger } from '../utils/logger';
import { ajv, formatValidationErrors } from '../i18n';

/**
 * 测试重置控制器
 * 处理测试环境下的游戏状态重置请求
 * 仅在开发环境下可用
 */
class TestResetController {

  /**
   * 重置用户游戏状态
   * POST /api/test/reset-game-state
   */
  public async resetGameState(req: MyRequest, res: Response): Promise<void> {
    const startTime = Date.now();
    const requestId = `reset_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      // 记录环境信息（不再限制环境）
      logger.info('重置API请求环境信息', {
        requestId,
        environment: testResetService.getCurrentEnvironment(),
        userAgent: req.headers['user-agent'],
        ip: req.ip
      });

      // 身份验证检查
      const walletId = Number(req.user?.walletId);
      if (!walletId) {
        logger.warn('重置游戏状态请求缺少用户身份验证', { requestId });
        res.status(401).json(errorResponse(
          tFromRequest(req, 'errors.unauthorized') || '用户未登录'
        ));
        return;
      }

      // 记录重置请求
      logger.info('收到重置游戏状态请求', {
        requestId,
        walletId,
        userAgent: req.headers['user-agent'],
        ip: req.ip,
        timestamp: new Date().toISOString()
      });

      // 验证重置前置条件
      const validationResult = await testResetService.validateResetPreconditions(walletId);

      logger.info('重置操作前置条件验证', {
        requestId,
        walletId,
        validationResult
      });

      // 如果验证失败，返回错误
      if (!validationResult.isValid) {
        logger.error('重置操作前置条件验证失败', {
          requestId,
          walletId,
          errors: validationResult.errors
        });

        res.status(400).json(errorResponse(
          validationResult.errors.join('; ')
        ));
        return;
      }

      // 如果有警告，记录但继续执行
      if (validationResult.warnings.length > 0) {
        logger.warn('重置操作警告', {
          requestId,
          walletId,
          warnings: validationResult.warnings
        });
      }

      // 获取重置前的状态信息
      const beforeResetInfo = await testResetService.getResetSafetyInfo(walletId);

      // 执行重置操作
      logger.info('开始执行游戏状态重置', {
        requestId,
        walletId,
        beforeReset: beforeResetInfo
      });

      const resetResult = await testResetService.resetUserGameState(walletId);

      const executionTime = Date.now() - startTime;

      // 获取重置后的状态信息
      const afterResetInfo = await testResetService.getResetSafetyInfo(walletId);

      // 记录审计日志
      testResetService.logResetAudit(
        walletId,
        'FULL_GAME_STATE_RESET',
        beforeResetInfo,
        afterResetInfo,
        {
          requestId,
          ip: req.ip,
          userAgent: req.headers['user-agent'],
          timestamp: new Date()
        }
      );

      // 记录重置成功
      logger.info('游戏状态重置成功', {
        requestId,
        walletId,
        executionTime,
        result: {
          farmPlotsCount: resetResult.farmPlots.length,
          unlockedFarmPlots: resetResult.farmPlots.filter(p => p.isUnlocked).length,
          deliveryLineReset: !!resetResult.deliveryLine,
          resetTimestamp: resetResult.resetTimestamp
        },
        beforeState: beforeResetInfo,
        afterState: afterResetInfo
      });

      // 简化返回数据
      const responseData = {
        resetTimestamp: resetResult.resetTimestamp
      };

      res.json(successResponse(
        responseData,
        tFromRequest(req, 'success.gameStateReset') || '游戏状态重置成功'
      ));

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      logger.error('重置游戏状态失败', {
        requestId,
        walletId: req.user?.walletId,
        executionTime,
        error: error instanceof Error ? {
          message: error.message,
          stack: error.stack
        } : error
      });

      // 根据错误类型返回不同的状态码
      let statusCode = 500;
      let errorMessage = tFromRequest(req, 'errors.serverError') || '服务器内部错误';

      if (error instanceof Error) {
        if (error.message.includes('用户钱包不存在')) {
          statusCode = 404;
          errorMessage = tFromRequest(req, 'errors.userNotFound') || '用户不存在';
        } else if (error.message.includes('权限')) {
          statusCode = 403;
          errorMessage = tFromRequest(req, 'errors.forbidden') || '权限不足';
        }
      }

      res.status(statusCode).json(errorResponse(errorMessage));
    }
  }

  /**
   * 获取重置操作的安全检查信息
   * GET /api/test/reset-safety-info
   */
  public async getResetSafetyInfo(req: MyRequest, res: Response): Promise<void> {
    try {
      // 记录环境信息（不再限制环境）
      logger.info('获取重置安全检查信息', {
        environment: testResetService.getCurrentEnvironment(),
        walletId: req.user?.walletId
      });

      // 身份验证检查
      const walletId = Number(req.user?.walletId);
      if (!walletId) {
        res.status(401).json(errorResponse(
          tFromRequest(req, 'errors.unauthorized') || '用户未登录'
        ));
        return;
      }

      const safetyInfo = await testResetService.getResetSafetyInfo(walletId);
      
      logger.info('获取重置安全检查信息', {
        walletId,
        safetyInfo
      });

      res.json(successResponse(safetyInfo));

    } catch (error) {
      logger.error('获取重置安全检查信息失败', {
        walletId: req.user?.walletId,
        error: error instanceof Error ? error.message : '未知错误'
      });

      res.status(500).json(errorResponse(
        tFromRequest(req, 'errors.serverError') || '服务器内部错误'
      ));
    }
  }

  /**
   * 健康检查接口
   * GET /api/test/health
   */
  public async healthCheck(req: Request, res: Response): Promise<void> {
    try {
      const healthInfo = {
        status: 'ok',
        environment: testResetService.getCurrentEnvironment(),
        isDevelopment: testResetService.getCurrentEnvironment() === 'development',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      };

      res.json(successResponse(healthInfo));
    } catch (error) {
      res.status(500).json(errorResponse('健康检查失败'));
    }
  }
}

export default new TestResetController();
