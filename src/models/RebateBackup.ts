// src/models/RebateBackup.ts
import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface RebateBackupAttributes {
  id: number;
  originalId: number;
  userId: number;
  walletId: number;
  amount: number;
  level: number;
  sourceUserId: number;
  backupDate: Date;
  originalCreatedAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

type RebateBackupCreationAttributes = Optional<RebateBackupAttributes, "id">;

export class RebateBackup
  extends Model<RebateBackupAttributes, RebateBackupCreationAttributes>
  implements RebateBackupAttributes
{
  public id!: number;
  public originalId!: number;
  public userId!: number;
  public walletId!: number;
  public amount!: number;
  public level!: number;
  public sourceUserId!: number;
  public backupDate!: Date;
  public originalCreatedAt!: Date;
  public createdAt!: Date;
  public updatedAt!: Date;
}

RebateBackup.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    originalId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    userId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    amount: {
      type: DataTypes.DECIMAL(18, 6),
      allowNull: false,
    },
    level: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    sourceUserId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    backupDate: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    originalCreatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: "rebate_backups",
    sequelize,
    timestamps: true,
  }
);