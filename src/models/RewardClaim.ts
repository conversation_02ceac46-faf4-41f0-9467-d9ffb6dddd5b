import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface RewardClaimAttributes {
  id: number;
  userId: number;
  walletId: number;
  prizePoolId: number;
  subPool?: string; // 子奖池名称 (例如 "one_star", "silver")
  amount: number; // 奖励金额
  claimed: boolean; // 是否已领取
  claimTime?: Date; // 领取时间
  createdAt?: Date;
  updatedAt?: Date;
}

type RewardClaimCreationAttributes = Optional<RewardClaimAttributes, "id">;

export class RewardClaim
  extends Model<RewardClaimAttributes, RewardClaimCreationAttributes>
  implements RewardClaimAttributes
{
  public id!: number;
  public userId!: number;
  public walletId!: number;
  public prizePoolId!: number;
  public subPool?: string;
  public amount!: number;
  public claimed!: boolean;
  public claimTime?: Date;

  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

RewardClaim.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    prizePoolId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    subPool: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    amount: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
    },
    claimed: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    claimTime: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: "reward_claims",
    sequelize,
    timestamps: true,
  }
);