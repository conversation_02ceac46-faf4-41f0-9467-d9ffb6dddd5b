import { DataTypes, Model } from "sequelize";
import { sequelize } from "../config/db";

export interface BullUnlockHistoryAttributes {
  id?: number;
  userId: number; // 用户 ID
  unlockDate: Date; // 解锁日期
  totalAmount: number; // 总金额
  availableAmount: number; // 当前可用金额
  withdrawnAmount: number; // 已提现金额
}

export class BullUnlockHistory extends Model<BullUnlockHistoryAttributes> implements BullUnlockHistoryAttributes {
  public id!: number;
  public userId!: number;
  public unlockDate!: Date;
  public totalAmount!: number;
  public availableAmount!: number;
  public withdrawnAmount!: number;
}

BullUnlockHistory.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    unlockDate: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    totalAmount: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    availableAmount: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    withdrawnAmount: {
      type: DataTypes.FLOAT,
      defaultValue: 0.0,
      allowNull: false,
    },
  },
  {
    tableName: "bull_unlock_histories",
    timestamps: true,
    sequelize,
  }
);

export default BullUnlockHistory;