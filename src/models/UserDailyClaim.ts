// src/models/UserDailyClaim.ts
import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface UserDailyClaimAttributes {
  id: number;
  walletId: number;
  userId: number;
  date: string; // "YYYY-MM-DD" 形式
  invitesNeededLevel?: number; // 记录领取时的邀请级别
  createdAt?: Date;
  updatedAt?: Date;
}

type UserDailyClaimCreationAttributes = Optional<
  UserDailyClaimAttributes,
  "id"
>;

export class UserDailyClaim
  extends Model<UserDailyClaimAttributes, UserDailyClaimCreationAttributes>
  implements UserDailyClaimAttributes
{
  public id!: number;
  public walletId!: number;
  public userId!: number;
  public date!: string; // YYYY-MM-DD
  public invitesNeededLevel!: number; // 记录领取时的邀请级别
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

UserDailyClaim.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    userId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    date: {
      type: DataTypes.STRING, // 存 '2025-01-15' 这种
      allowNull: false,
    },
    invitesNeededLevel: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true,
      comment: '记录用户领取时的邀请级别(invitesNeeded)',
    },
  },
  {
    tableName: "user_daily_claims",
    sequelize,
    timestamps: true,
  }
);
