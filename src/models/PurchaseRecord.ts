// src/models/PurchaseRecord.ts
import { Model, DataTypes } from 'sequelize';
import {sequelize} from '../config/db';

export class PurchaseRecord extends Model {
  public id!: number;
  public user_id!: number;
  public amount!: number;
  public currency!: string;
  public payment_id!: string;
  public telegram_payment_charge_id!: string;
  public invoice_payload!: string;
  public purchase_date!: string;
  public status!: string; // 新增状态字段，用于跟踪支付状态
  public product_type?: string; // 新增产品类型字段
  public benefits_delivered!: boolean; // 新增权益发放状态字段

  // 时间戳
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

PurchaseRecord.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    amount: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    currency: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    payment_id: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    telegram_payment_charge_id: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    invoice_payload: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    purchase_date: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    status: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'completed', // 默认状态为已完成
    },
    product_type: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    benefits_delivered: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false, // 默认权益未发放
    },
  },
  {
    sequelize,
    modelName: 'PurchaseRecord',
    tableName: 'purchase_records',
    timestamps: true,
  }
);