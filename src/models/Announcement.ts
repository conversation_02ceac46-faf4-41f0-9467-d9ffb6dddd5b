// src/models/Announcement.ts
import { Model, DataTypes } from "sequelize";
import { sequelize } from "../config/db";

export interface AnnouncementAttributes {
  id?: number;
  userId: number;
  walletId: number;
  //奖励金额
  amount: number;
  //奖励币种
  currency: string;
  //奖励类型
  title: string;
  content: string;
  type: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export class Announcement extends Model<AnnouncementAttributes> implements AnnouncementAttributes {
  public id!: number;
  public userId!: number;
  public walletId!: number;
  public amount!: number;
  public currency!: string;
  public title!: string;
  public content!: string;
  public type!: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

Announcement.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    walletId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    amount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    currency: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'usd',
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'bull_king',
    },
  },
  {
    tableName: "announcements",
    sequelize,
    timestamps: true,
  }
);

export default Announcement;