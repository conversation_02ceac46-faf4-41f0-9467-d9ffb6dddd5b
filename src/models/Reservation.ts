import { DataTypes, Model } from "sequelize";
import { sequelize } from "../config/db";

export type ReservationStatus = "reserved" | "refunded" | "success" | "failed";

export interface ReservationAttributes {
  id?: number;
  userId: number;
  walletId: number;
  roundIndex: number;
  sessionId: number;
  roomId: number;
  roomNumber: number;
  player_index?: number;
  status: ReservationStatus;
  reservedAt: Date;
  ticketType?: string; // 添加票类型字段：ticket(普通票)或free_ticket(免费票)
}

export class Reservation
  extends Model<ReservationAttributes>
  implements ReservationAttributes
{
  public id!: number;
  public userId!: number;
  public walletId!: number;
  public sessionId!: number;
  public roundIndex!: number;
  public player_index?: number;
  public roomId!: number;
  public roomNumber!: number;
  public status!: ReservationStatus;
  public reservedAt!: Date;
  public ticketType!: string; // 添加票类型字段
  UserWallet: any;
}

Reservation.init(
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    userId: { type: DataTypes.INTEGER.UNSIGNED, allowNull: false },
    walletId: { type: DataTypes.INTEGER.UNSIGNED, allowNull: false },
    sessionId: { type: DataTypes.INTEGER, allowNull: false },
    roundIndex: { type: DataTypes.INTEGER.UNSIGNED, allowNull: false },
    roomId: { type: DataTypes.INTEGER, allowNull: false },
    roomNumber: { type: DataTypes.INTEGER, allowNull: false },
    player_index: { type: DataTypes.INTEGER, allowNull: false ,defaultValue:0 }, // 座位号
    status: {
      type: DataTypes.ENUM("reserved", "refunded","success",'failed'),
      defaultValue: "reserved",
    },
    reservedAt: { type: DataTypes.DATE, defaultValue: DataTypes.NOW },
    ticketType: { 
      type: DataTypes.STRING, 
      allowNull: false, 
      defaultValue: 'ticket',
      comment: '使用的票类型: ticket(普通票) 或 free_ticket(免费票)'
    },
  },
  {
    tableName: "reservations",
    sequelize,
  }
);

export default Reservation;
