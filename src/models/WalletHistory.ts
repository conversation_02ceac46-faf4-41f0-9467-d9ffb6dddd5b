// src/models/WalletHistory.ts
import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface WalletHistoryAttributes {
  id: number;
  userId: number;
  walletId: number;
  currency: string; // "COIN", "TICKET", "TON", "GEM"等
  amount: number; // 变动数量 (正负)
  reference: string; // 备注/类型: "购买票", "存款", "幸运抽奖", ...
  action: string; // "IN" 或 "OUT"
  category: string; // "DEPOSIT", "WITHDRAWAL"等
  credit_type: string; // "DEPOSIT", "WITHDRAWAL"等
  fe_display_remark: string; // 前端展示备注
  developer_remark: string; // 开发者备注
  createdAt?: Date;
  updatedAt?: Date;
  // 在现有的WalletHistory模型中添加以下字段
  
  // 在WalletHistoryAttributes接口中添加
  fee?: number;
  status?: string;
  withdrawalAddress?: string;
}

type WalletHistoryCreationAttributes = Optional<WalletHistoryAttributes, "id">;

export class WalletHistory
  extends Model<WalletHistoryAttributes, WalletHistoryCreationAttributes>
  implements WalletHistoryAttributes
{
  public id!: number;
  public userId!: number;
  public walletId!: number;
  public currency!: string;
  public amount!: number;
  public reference!: string;
  public action!: string;
  public category!: string;
  public credit_type!: string;
  public fee?: number;
  public status?: string;
  public withdrawalAddress?: string;
  
  public fe_display_remark!: string;
  public developer_remark!: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

WalletHistory.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    currency: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    amount: {
      type: DataTypes.DECIMAL(18, 6),
      allowNull: false,
    },
    reference: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    action: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    category: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    credit_type: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    fee: {
      type: DataTypes.DECIMAL(18, 6),
      allowNull: true,
    },
    status: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    withdrawalAddress: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    fe_display_remark: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    developer_remark: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  {
    tableName: "wallet_history",
    sequelize,
    timestamps: true,
    // 在这里指定 indexes
    indexes: [
      {
        name: "idx_walletId", // 索引名称，可随意命名
        fields: ["walletId"], // 哪个字段加索引
      },
    ],
  }
);
