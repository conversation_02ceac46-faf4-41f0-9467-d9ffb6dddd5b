import { Model, DataTypes } from 'sequelize';
import { sequelize } from '../config/db';

export class UsdtDepositHistory extends Model {
  public id!: number;
  public userId!: number;
  public walletId!: number;
  public walletAddress!: string;
  public transactionHash!: string;
  public amount!: number;
  public jettonType!: string;
  public timestamp!: number;
  
  public status!: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

UsdtDepositHistory.init({
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  walletId: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  walletAddress: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  transactionHash: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  jettonType: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  amount: {
    type: DataTypes.DECIMAL(20, 8),
    allowNull: false,
  },
  timestamp: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  status: {
    type: DataTypes.STRING,
    allowNull: false,
  },
}, {
  sequelize,
  tableName: 'usdt_deposit_histories',
  timestamps: true,
});