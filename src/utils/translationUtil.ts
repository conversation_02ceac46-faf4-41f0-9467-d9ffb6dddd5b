import { t, SUPPORTED_LANGUAGES, SupportedLanguage } from '../i18n';

/**
 * 手动替换翻译文本中的变量
 * @param text 原始文本
 * @param variables 变量对象
 * @returns 替换后的文本
 */
export function manualReplaceVariables(text: string, variables: Record<string, any>): string {
  let result = text;
  Object.entries(variables).forEach(([key, value]) => {
    result = result.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
  });
  return result;
}

/**
 * 获取并替换翻译文本中的变量
 * @param key 翻译键
 * @param variables 变量对象
 * @param lang 语言代码
 * @returns 替换后的文本
 */
export function tWithVariables(key: string, variables: Record<string, any>, lang: SupportedLanguage = 'zh'): string {
  // 获取原始翻译模板
  const template = t(key, {}, lang);
  
  // 手动替换变量
  return manualReplaceVariables(template, variables);
}

/**
 * 处理自定义错误消息，提取参数并返回翻译后的文本
 * @param errorMessage 错误消息
 * @param lang 语言代码
 * @returns 翻译后的文本
 */
export function processErrorMessage(errorMessage: string, lang: SupportedLanguage = 'zh'): string {
  // 处理宝箱数量无效错误
  if (errorMessage === 'CHEST_OPEN_COUNT_INVALID') {
    return tWithVariables('errors.chestOpenCountInvalid', { count: 0 }, lang);
  }
  
  // 处理宝箱数量不足错误
  if (errorMessage.startsWith('NOT_ENOUGH_CHESTS:')) {
    const parts = errorMessage.split(':');
    if (parts.length === 3) {
      const required = Number(parts[1]);
      const available = Number(parts[2]);
      return tWithVariables('errors.notEnoughChests', { required, available }, lang);
    }
  }
  
  // 默认返回原始错误消息
  return errorMessage;
} 