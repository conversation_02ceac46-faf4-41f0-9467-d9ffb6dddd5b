// src/services/walletService.ts
import { UserWallet } from "../models/UserWallet";
import { WalletHistory } from "../models/WalletHistory";
import { sequelize } from "../config/db";
import { Transaction } from "sequelize";
import { DeliveryLine } from "../models/DeliveryLine";
import { createBigNumber } from "../utils/bigNumberConfig";

/**
 * 钱包服务类，处理与用户钱包相关的业务逻辑
 */
class WalletService {
  /**
   * 增加用户钱包的牛奶值和待处理牛奶
   * @param userId 用户ID
   * @param walletId 钱包ID
   * @param milkAmount 要增加的牛奶数量
   * @param pendingMilk 要增加的待处理牛奶数量（可选）
   * @returns 更新后的钱包信息
   */
  public async increaseMilk(
    userId: number,
    walletId: number,
    milkAmount: number,
    pendingMilk?: number
  ): Promise<{ milk: number; pendingMilk?: number }> {
    const transaction: Transaction = await sequelize.transaction();

    try {
      // 查找用户钱包
      const wallet = await UserWallet.findOne({
        where: { id: walletId, userId },
        transaction
      });

      if (!wallet) {
        await transaction.rollback();
        throw new Error("找不到用户钱包");
      }

      // 更新钱包牛奶值
      wallet.milk += milkAmount;
      await wallet.save({ transaction });

      // 创建钱包历史记录
      await WalletHistory.create({
        userId,
        walletId,
        currency: "MILK",
        amount: milkAmount,
        reference: "增加牛奶",
        action: "IN",
        category: "ADMIN_ADJUSTMENT",
        credit_type: "ADMIN_ADJUSTMENT",
        fe_display_remark: `增加了 ${milkAmount} 牛奶`,
        developer_remark: `通过API增加牛奶: ${milkAmount}`
      }, { transaction });

      // 如果提供了pendingMilk参数，则更新DeliveryLine中的pendingMilk
      let updatedPendingMilk;
      if (pendingMilk !== undefined && pendingMilk > 0) {
        const deliveryLine = await DeliveryLine.findOne({
          where: { walletId },
          transaction
        });

        if (deliveryLine) {
          deliveryLine.pendingMilk = (deliveryLine.pendingMilk || 0) + pendingMilk;
          await deliveryLine.save({ transaction });
          updatedPendingMilk = deliveryLine.pendingMilk;
        }
      }

      await transaction.commit();

      return {
        milk: wallet.milk,
        ...(updatedPendingMilk !== undefined ? { pendingMilk: updatedPendingMilk } : {})
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 增加用户钱包的宝石值
   * @param userId 用户ID
   * @param walletId 钱包ID
   * @param gemAmount 要增加的宝石数量
   * @returns 更新后的钱包信息
   */
  public async increaseGem(
    userId: number,
    walletId: number,
    milkAmount: number
  ): Promise<{ gem: number; pendingMilk: number; milkUsed: number; gemAmount: number }> {
    const transaction: Transaction = await sequelize.transaction();

    try {
      // 查找用户钱包
      const wallet = await UserWallet.findOne({
        where: { id: walletId, userId },
        transaction
      });

      if (!wallet) {
        await transaction.rollback();
        throw new Error("找不到用户钱包");
      }

      // 查找用户的DeliveryLine
      const deliveryLine = await DeliveryLine.findOne({
        where: { walletId },
        transaction
      });

      if (!deliveryLine) {
        await transaction.rollback();
        throw new Error("找不到用户的配送线");
      }

      // 检查用户是否有足够的待处理牛奶
      const requestedMilkAmount = Number(milkAmount);
      if (deliveryLine.pendingMilk < requestedMilkAmount) {
        await transaction.rollback();
        throw new Error("待处理牛奶不足");
      }

      // 使用1:1的兑换比例，直接将牛奶等量转换为宝石
      const gemAmount = requestedMilkAmount;
      const milkUsed = requestedMilkAmount;

      // 更新待处理牛奶数量
      deliveryLine.pendingMilk -= milkUsed;
      await deliveryLine.save({ transaction });

      // 更新钱包宝石值
      const currentGemBN = createBigNumber(wallet.gem || 0);
      const gemAmountBN = createBigNumber(gemAmount);
      const newGemBN = currentGemBN.plus(gemAmountBN);
      wallet.gem = newGemBN.toFixed(3);
      await wallet.save({ transaction });

      // 创建钱包历史记录 - 牛奶消耗
      await WalletHistory.create({
        userId,
        walletId,
        currency: "MILK",
        amount: -milkUsed,
        reference: "牛奶兑换宝石",
        action: "OUT",
        category: "MILK_TO_GEM",
        credit_type: "CONVERSION",
        fe_display_remark: `消耗了 ${milkUsed} 牛奶兑换宝石`,
        developer_remark: `将牛奶兑换为宝石: ${milkUsed} 牛奶`
      }, { transaction });

      // 创建钱包历史记录 - 宝石增加
      await WalletHistory.create({
        userId,
        walletId,
        currency: "GEM",
        amount: gemAmount,
        reference: "增加宝石",
        action: "IN",
        category: "MILK_TO_GEM",
        credit_type: "CONVERSION",
        fe_display_remark: `增加了 ${gemAmount} 宝石`,
        developer_remark: `通过牛奶兑换获得宝石: ${gemAmount} 宝石`
      }, { transaction });

      await transaction.commit();

      return { 
        gem: Number(wallet.gem || 0),
        pendingMilk: deliveryLine.pendingMilk,
        milkUsed,
        gemAmount
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}

export default new WalletService();