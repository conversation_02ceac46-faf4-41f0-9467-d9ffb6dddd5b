// src/services/fragmentService.ts

import { UserWallet } from "../models/UserWallet";
import { Op, Sequelize, Transaction } from "sequelize";
import { sequelize } from "../config/db";
import { recordWalletHistory } from "./walletHistoryService";
import { t } from "../i18n";

// 碎片制作配置
const FRAGMENT_CRAFT_CONFIG = {
  // 碎片类型及其兑换比例
  GREEN: {
    required: 160,  // 所需数量
    tickets: 1      // 可制作门票数
  },
  BLUE: {
    required: 80,   // 所需数量
    tickets: 3      // 可制作门票数
  },
  PURPLE: {
    required: 40,   // 所需数量
    tickets: 15     // 可制作门票数
  },
  GOLD: {
    required: 20,   // 所需数量
    tickets: 60     // 可制作门票数
  }
};

/**
 * 碎片类型枚举
 */
export enum FragmentType {
  GREEN = 'fragment_green',
  BLUE = 'fragment_blue',
  PURPLE = 'fragment_purple',
  GOLD = 'fragment_gold'
}

/**
 * 使用碎片制作门票
 * @param userId 用户ID
 * @param walletId 钱包ID
 * @param fragmentType 碎片类型
 * @param quantity 要使用的碎片数量
 * @param isFree 是否为免费门票，默认为false
 * @returns 更新后的钱包信息
 */
export async function craftTicketWithFragment(
  userId: number,
  walletId: number,
  fragmentType: FragmentType,
  quantity: number,
  isFree: boolean = true
) {
  // 验证数量是否为正数
  if (quantity <= 0) {
    throw new Error(t("errors.quantityMustBePositive"));
  }

  // 获取对应碎片类型的配置
  let config;
  switch (fragmentType) {
    case FragmentType.GREEN:
      config = FRAGMENT_CRAFT_CONFIG.GREEN;
      break;
    case FragmentType.BLUE:
      config = FRAGMENT_CRAFT_CONFIG.BLUE;
      break;
    case FragmentType.PURPLE:
      config = FRAGMENT_CRAFT_CONFIG.PURPLE;
      break;
    case FragmentType.GOLD:
      config = FRAGMENT_CRAFT_CONFIG.GOLD;
      break;
    default:
      throw new Error(t("errors.invalidFragmentType"));
  }

  // 计算需要的碎片总数和可以获得的门票数
  const totalFragmentsRequired = quantity * config.required;
  const ticketsToAdd = quantity * config.tickets;

  // 使用事务确保操作的原子性
  const transaction = await sequelize.transaction();

  try {
    // 核心：使用 update + where fragment_xxx >= totalFragmentsRequired
    const [affectedCount] = await UserWallet.update(
      {
        // 减少对应类型的碎片数量
        [fragmentType]: Sequelize.literal(`${fragmentType} - ${totalFragmentsRequired}`),
        // 根据isFree参数决定增加普通门票还是免费门票
        ...(isFree 
          ? { free_ticket: Sequelize.literal(`free_ticket + ${ticketsToAdd}`) }
          : { ticket: Sequelize.literal(`ticket + ${ticketsToAdd}`) }),
      },
      {
        where: {
          id: walletId,
          [fragmentType]: {
            [Op.gte]: totalFragmentsRequired, // 保证只有当碎片足够时才更新
          },
        },
        transaction
      }
    );

    // 如果 affectedCount=0，说明没有任何记录被更新，要么钱包不存在，要么碎片不足
    if (affectedCount === 0) {
      // console.log(t("errors.insufficientFragments"));
      
      // 直接抛出错误，让外层的catch块统一处理事务回滚
      throw new Error(t("errors.insufficientFragments"));
    }

    // 记录钱包历史 - 碎片减少
    await recordWalletHistory(
      userId,
      walletId,
      fragmentType,
      totalFragmentsRequired,
      `${fragmentType} Used for Craft`,
      'out',
      fragmentType,
      fragmentType,
      'Fragment Craft',
      'fragment_craft',
      transaction
    );

    // 记录钱包历史 - 门票增加（根据是否为免费门票区分）
    await recordWalletHistory(
      userId,
      walletId,
      isFree ? "free_ticket" : "ticket",
      ticketsToAdd,
      isFree ? "Free Ticket from Fragment Craft" : "Ticket from Fragment Craft",
      "in",
      isFree ? "free_ticket" : "ticket",
      isFree ? "free_ticket" : "ticket",
      "Fragment Craft",
      "fragment_craft",
      transaction
    );

    // 提交事务
    await transaction.commit();

    // 返回更新后的钱包信息
    const updatedWallet = await UserWallet.findByPk(walletId);
    return updatedWallet;
  } catch (error) {
    // 如果发生错误，回滚事务
    await transaction.rollback();
    throw error;
  }
}