import { ethers } from 'ethers';
import jwt from 'jsonwebtoken';
import { User } from '../models/User';
import { UserWallet } from '../models/UserWallet';
import { generateUniqueCode } from '../utils/random';
import { sequelize } from '../config/db';
import { t } from '../i18n';

// JWT 密钥
const JWT_SECRET = process.env.JWT_SECRET_Wallet!;

/**
 * 验证以太坊钱包签名
 * @param message 签名的消息
 * @param signature 签名
 * @param walletAddress 钱包地址
 * @returns 是否验证成功
 */
export async function verifyWeb3Signature(
  message: string,
  signature: string,
  walletAddress: string
): Promise<boolean> {
  try {
    // 使用ethers.js验证签名    
    const recoveredAddress = ethers.verifyMessage(message, signature);
    // 验证恢复的地址是否与提供的钱包地址匹配（不区分大小写）
    return recoveredAddress.toLowerCase() === walletAddress.toLowerCase();
  } catch (error) {
    console.error('验证签名失败:', error);
    return false;
  }
}

/**
 * 生成用于签名的消息
 * @param nonce 随机数
 * @returns 签名消息
 */
export function generateSignMessage(nonce: string): string {
  return `Welcome to MooFun!\n\nPlease sign to verify your wallet address, after which you can begin your wonderful journey.\n\nSecurity code: ${nonce}\nTimestamp: ${Date.now()}`;
}

/**
 * 生成JWT令牌
 * @param userId 用户ID
 * @param walletId 钱包ID
 * @param walletAddress 钱包地址
 * @returns JWT令牌
 */
export function generateToken(
  userId: number,
  walletId: number,
  walletAddress: string
): string {
  return jwt.sign(
    { userId, walletId, walletAddress },
    JWT_SECRET,
    { expiresIn: '60d' }
  );
}

/**
 * 生成唯一的邀请码
 * @returns 唯一邀请码
 */
async function generateUniqueUsername(): Promise<string> {
  const MAX_TRIES = 100;
  for (let i = 0; i < MAX_TRIES; i++) {
    // 生成一个更随机的用户名，例如结合时间戳和随机字符串
    const candidate = `user_${Date.now().toString(36)}${Math.random().toString(36).substring(2, 7)}`;
    const existing = await User.findOne({ where: { username: candidate } });
    if (!existing) return candidate;
  }
  throw new Error(t('errors.failedToGenerateUniqueUsername'));
}

async function getUniqueInviteCode(): Promise<string> {
  const MAX_TRIES = 100;
  for (let i = 0; i < MAX_TRIES; i++) {
    const candidate = generateUniqueCode(6);
    const existing = await UserWallet.findOne({ where: { code: candidate } });
    if (!existing) return candidate;
  }
  throw new Error(t('errors.failedToGenerateUniqueCode'));
}

/**
 * Web3钱包登录
 * @param walletAddress 钱包地址
 * @param signature 签名
 * @param message 签名消息
 * @param referralCode 推荐码（可选）
 * @returns 包含用户信息和令牌的对象
 */
export async function web3Login(
  walletAddress: string,
  signature: string,
  message: string,
  referralCode?: string
): Promise<{ user: User; wallet: UserWallet; token: string }> {

  
  // 验证签名（开发环境下跳过验证）
  const isDevelopment = process.env.NODE_ENV === 'development';
  let isValidSignature = false;

  if (isDevelopment) {
    // 开发环境下，允许特定的测试钱包地址跳过签名验证
    const testWalletAddress = '******************************************';
    isValidSignature = walletAddress.toLowerCase() === testWalletAddress.toLowerCase();
  } else {
    isValidSignature = await verifyWeb3Signature(message, signature, walletAddress);
  }

  if (!isValidSignature) {
    throw new Error(t('errors.invalidSignature'));
  }

  // 使用事务确保数据一致性
  const result = await sequelize.transaction(async (transaction) => {
    // 查找钱包地址是否已存在
    let userWallet = await UserWallet.findOne({
      where: { walletAddress: walletAddress },
      transaction
    });

    let user: User;
    
    if (userWallet) {
      // 如果钱包已存在，获取关联的用户
      const foundUser = await User.findByPk(userWallet.userId, { transaction });
      if (!foundUser) {
        throw new Error(t('errors.userNotFound')); 
      }
      user = foundUser;
      if (!user) {
        throw new Error(t('errors.userNotFound'));
      }
    } else {
      // 处理推荐人逻辑
      let referrerWallet = null;
      if (referralCode) {
        referrerWallet = await UserWallet.findOne({
          where: { code: referralCode },
          transaction
        });
        
        if (!referrerWallet) {
          throw new Error(t('errors.invitationCodeNotExist'));
        }
      }

      // 创建新用户，使用钱包地址的前8位和后8位作为用户名
      const username = await generateUniqueUsername();
      user = await User.create({
        telegramId: `web3_${walletAddress.toLowerCase()}`, // 使用钱包地址作为唯一标识
        username: username,
        hasFollowedChannel: false,
        authDate: Math.floor(Date.now() / 1000),
        hash: '', // 不需要hash
        refWalletAddress: referrerWallet?.walletAddress,
        referrerId: referrerWallet?.userId
      }, { transaction });

      // 创建用户钱包
      const inviteCode = await getUniqueInviteCode();
      userWallet = await UserWallet.create({
        userId: user.id,
        walletAddress: walletAddress,
        parsedWalletAddress: walletAddress.toLowerCase(),
        code: inviteCode,
        ton: 0,
        gem: 0,
        ticket: 0,
        free_ticket: 0,
        usd: 0,
        moof: 0,
        milk: 0,
        referrerWalletId: referrerWallet?.id
      }, { transaction });

      // 更新用户的firstWalletId
      await user.update({ firstWalletId: userWallet.id }, { transaction });

      // 如果有推荐人，增加推荐人的推荐计数
      if (referrerWallet) {
        await User.increment(
          { referralCount: 1 },
          { where: { id: referrerWallet.userId }, transaction }
        );
      }
    }

    // 生成JWT令牌
    const token = generateToken(user.id, userWallet.id, walletAddress);

    return { user, wallet: userWallet, token };
  });

  return result;
}

/**
 * 更新用户名
 * @param userId 用户ID
 * @param newUsername 新用户名
 * @returns 更新后的用户对象
 */
export async function updateUsername(
  userId: number,
  newUsername: string
): Promise<User> {
  // 检查用户名是否已存在
  const existingUser = await User.findOne({
    where: { username: newUsername }
  });
  if (existingUser && existingUser.id !== userId) {
    throw new Error(t('errors.usernameAlreadyExists'));
  }

  // 查找用户
  const user = await User.findByPk(userId);
  if (!user) {
    throw new Error(t('errors.userNotFound'));
  }

  // 更新用户名
  user.username = newUsername;
  await user.save();

  return user;
}