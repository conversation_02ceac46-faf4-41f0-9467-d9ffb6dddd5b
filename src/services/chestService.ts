// src/services/chestService.ts
import { Chest } from "../models/Chest";
import { UserWallet } from "../models/UserWallet";
import { sequelize } from "../config/db";
import { WalletHistory, Announcement, User, ShareBoostLink, JackpotPool } from "../models";
import { t } from "../i18n";
import { checkAndAdvanceJackpotLevel, createShareBoostLink, getActiveJackpotPool, processReferralBoost } from "./jackpotChestService";
import { Op, Transaction } from "sequelize";
import { v4 as uuidv4 } from "uuid";
import BigNumber from 'bignumber.js';

/**
 * 生成宝箱奖励
 * 根据设定的概率生成不同等级的宝箱奖励
 * LV1宝箱(60%): 绿色碎片16-24(100%), 蓝色碎片1-4(30%), 宝石1,000-5,000(100%)
 * LV2宝箱(28%): 绿色碎片8-15(100%), 蓝色碎片4-8(100%), 紫色碎片0-1(20%), 宝石5,000-15,000(100%)
 * LV3宝箱(10%): 蓝色碎片6-12(100%), 紫色碎片2-4(100%), 金色碎片0-1(28%), 宝石20,000-50,000(100%)
 * LV4宝箱(2%): 紫色碎片6-10(100%), 金色碎片2-4(100%), 宝石50,000-100,000(100%)
 * @returns {Object} 宝箱奖励信息
 */
export function generateChestReward() {
  // 生成随机数在指定范围内
  const getRandomInt = (min: number, max: number) => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  };

  // 根据概率决定是否添加物品
  const shouldAddItem = (probability: number) => {
    return Math.random() * 100 < probability;
  };

  const random = Math.random() * 100; // 转换为百分比
  
  const items = [];
  let level = 1;

  // LV1宝箱 - 60% 概率
  if (random < 60) {
    level = 1;
    
    // 绿色碎片16-24 (100%概率)
    items.push({
      type: 'fragment_green',
      amount: getRandomInt(16, 24)
    });
    
    // 蓝色碎片1-4 (30%概率)
    if (shouldAddItem(30)) {
      items.push({
        type: 'fragment_blue',
        amount: getRandomInt(1, 4)
      });
    }
    
    // 宝石1,000-5,000 (100%概率)
    items.push({
      type: 'gem',
      amount: getRandomInt(1000, 5000)
    });
  }
  // LV2宝箱 - 28% 概率
  else if (random < 88) { // 60 + 28 = 88
    level = 2;
    
    // 绿色碎片8-15 (100%概率)
    items.push({
      type: 'fragment_green',
      amount: getRandomInt(8, 15)
    });
    
    // 蓝色碎片4-8 (100%概率)
    items.push({
      type: 'fragment_blue',
      amount: getRandomInt(4, 8)
    });
    
    // 紫色碎片0-1 (20%概率)
    if (shouldAddItem(20)) {
      items.push({
        type: 'fragment_purple',
        amount: 1
      });
    }
    
    // 宝石5,000-15,000 (100%概率)
    items.push({
      type: 'gem',
      amount: getRandomInt(5000, 15000)
    });
  }
  // LV3宝箱 - 10% 概率
  else if (random < 98) { // 88 + 10 = 98
    level = 3;
    
    // 蓝色碎片6-12 (100%概率)
    items.push({
      type: 'fragment_blue',
      amount: getRandomInt(6, 12)
    });
    
    // 紫色碎片2-4 (100%概率)
    items.push({
      type: 'fragment_purple',
      amount: getRandomInt(2, 4)
    });
    
    // 金色碎片0-1 (28%概率)
    if (shouldAddItem(28)) {
      items.push({
        type: 'fragment_gold',
        amount: 1
      });
    }
    
    // 宝石20,000-50,000 (100%概率)
    items.push({
      type: 'gem',
      amount: getRandomInt(20000, 50000)
    });
  }
  // LV4宝箱 - 2% 概率
  else {
    level = 4;
    
    // 紫色碎片6-10 (100%概率)
    items.push({
      type: 'fragment_purple',
      amount: getRandomInt(6, 10)
    });
    
    // 金色碎片2-4 (100%概率)
    items.push({
      type: 'fragment_gold',
      amount: getRandomInt(2, 4)
    });
    
    // 宝石50,000-100,000 (100%概率)
    items.push({
      type: 'gem',
      amount: getRandomInt(50000, 100000)
    });
  }

  return {
    level,
    items
  };
}

export async function chestCount(walletId: number) {
  //获取可用宝箱总数
  const count = await Chest.count({
    where: { isOpened: false, walletId },
  });

  return count;
}

/**
 * 获取用户的宝箱记录，包括开启时间和奖励信息
 * @param userId 用户ID
 * @param walletId 钱包ID
 * @param page 页码，默认为1
 * @param limit 每页记录数，默认为20
 * @returns 宝箱记录列表和分页信息
 */
export async function getUserChestRecords(userId: number, walletId: number, page: number = 1, limit: number = 20) {
  try {
    // 1. 查找用户
    const user = await User.findByPk(userId);
    if (!user) {
      throw new Error(t("errors.userNotExist"));
    }

    // 2. 查找钱包
    const wallet = await UserWallet.findByPk(walletId);
    if (!wallet || wallet.userId !== userId) {
      throw new Error(t("errors.walletNotExist"));
    }

    // 防止出现负数或 0
    const safePage = page < 1 ? 1 : page;
    const safeLimit = limit < 1 ? 20 : limit;

    // 计算 offset
    const offset = (safePage - 1) * safeLimit;

    // 3. 查询用户的宝箱记录，按创建时间倒序排列
    const { rows, count } = await Chest.findAndCountAll({
      where: { userId, walletId, isOpened: true },
      attributes: [
        'id',
        'type',
        'source',
        'isOpened',
        'openedAt',
        'rewardInfo',
        'createdAt',
        'updatedAt'
      ],
      order: [['openedAt', 'DESC']],
      limit: safeLimit,
      offset
    });

    // 4. 计算总页数
    const totalPages = Math.ceil(count / safeLimit);

    return {
      records: rows,
      total: count,
      page: safePage,
      limit: safeLimit,
      totalPages
    };
  } catch (error) {
    console.error('获取宝箱记录失败:', error);
    throw error;
  }
}

interface ProcessOpenChestParams {
  userId: number;
  walletId: number;
  count?: number;
  chestId?: number;
  chestType?: string;
  transaction: Transaction;
}

/**
 * 综合处理打开宝箱的函数
 */
export async function processOpenChest(params: ProcessOpenChestParams) {
  const { userId, walletId, count, chestId, chestType, transaction } = params;


  try {
    let jackpotWinner = null;
    // 1. 查找用户
    const user = await User.findByPk(userId, { transaction });
    if (!user) {
      throw new Error(t("errors.userNotExist"));
    }

    // 2. 查找钱包
    const wallet = await UserWallet.findByPk(walletId, { transaction });
    if (!wallet || wallet.userId !== userId) {
      throw new Error(t("errors.walletNotExist"));
    }

    let whereCondition: any = {
      userId,
      walletId,
      isOpened: false
    };

    if (chestType) {
      whereCondition.type = chestType;
    }

    let unopenedChests;
    if (chestId) {
      whereCondition.id = chestId;
      unopenedChests = await Chest.findAll({
        where: whereCondition,
        transaction
      });
    } else {
      // 如果没有传入 count，则不设置 limit（获取全部宝箱）
      const queryOptions: any = {
        where: whereCondition,
        order: [['createdAt', 'ASC']],
        transaction
      };

      if (count) {
        queryOptions.limit = count;
      }

      unopenedChests = await Chest.findAll(queryOptions);
    }

    if (unopenedChests.length === 0) {
      throw new Error(t("errors.noChestsAvailable"));
    }

    const allRewards = [];
    const chestIds = [];
    const highLevelChests = []; // 存储3/4级宝箱

    console.log(`开始处理 ${unopenedChests.length} 个，钱包ID: ${walletId}`);

    // 为每个宝箱生成奖励并更新状态
    for (const chest of unopenedChests) {
      // 生成随机奖励
      const reward = generateChestReward();
      allRewards.push(reward);
      chestIds.push(chest.id);

      console.log(`宝箱ID: ${chest.id}, 生成等级 ${reward.level} 奖励`);

      // 更新宝箱状态为已开启，并保存奖励信息
      await chest.update({
        isOpened: true,
        openedAt: new Date(),
        rewardInfo: reward // 保存奖励信息
      }, { transaction });
      
      console.log(`已记录宝箱ID: ${chest.id} 的奖励信息`);

      // 如果是3级或4级宝箱，添加到高级宝箱列表
      if (reward.level >= 3) {
        // 为高级宝箱创建分享链接
        const shareCode = await createShareBoostLink(userId, walletId, chest.id, reward.level, transaction);
        console.log(`为${reward.level}级宝箱创建分享链接: ${shareCode}`);
        
        // 直接查询刚创建的分享链接并添加到结果中
        const newLink = await ShareBoostLink.findOne({
          where: {
            userId,
            chestId: chest.id,
            code: shareCode
          },
          transaction
        });
        
        if (newLink) {
          console.log(`成功查询到刚创建的分享链接: ${newLink.code}`);
          highLevelChests.push({
            chestId: chest.id,
            level: reward.level,
            shareLink: {
              chestId: newLink.chestId,
              chestLevel: newLink.chestLevel,
              shareCode: newLink.code,
              maxUses: newLink.maxUses,
              currentUses: newLink.currentUses,
              expiresAt: newLink.expiresAt
            }
          });
        } else {
          console.log(`警告: 无法查询到刚创建的分享链接, chestId: ${chest.id}, code: ${shareCode}`);
          highLevelChests.push({
            chestId: chest.id,
            level: reward.level
          });
        }
      }

      // 更新用户钱包
      for (const item of reward.items) {
        try {
          console.log(`处理奖励: 类型=${item.type}, 数量=${item.amount}`);

          // 使用increment方法更新钱包
          switch (item.type) {
            case 'gem':
              await UserWallet.increment('gem', {
                by: item.amount,
                where: { id: walletId },
                transaction
              });
              break;
            case 'ticket':
              await UserWallet.increment('ticket', {
                by: item.amount,
                where: { id: walletId },
                transaction
              });
              break;
            case 'fragment_green':
              await UserWallet.increment('fragment_green', {
                by: item.amount,
                where: { id: walletId },
                transaction
              });
              break;
            case 'fragment_blue':
              await UserWallet.increment('fragment_blue', {
                by: item.amount,
                where: { id: walletId },
                transaction
              });
              break;
            case 'fragment_purple':
              await UserWallet.increment('fragment_purple', {
                by: item.amount,
                where: { id: walletId },
                transaction
              });
              break;
            case 'fragment_gold':
              await UserWallet.increment('fragment_gold', {
                by: item.amount,
                where: { id: walletId },
                transaction
              });
              break;
            case 'ton':
              await UserWallet.increment('ton', {
                by: item.amount,
                where: { id: walletId },
                transaction
              });
              break;
            default:
              console.error(`未知的奖励类型: ${item.type}`);
              continue;
          }

          // 记录钱包历史
          await WalletHistory.create({
            userId,
            walletId,
            amount: item.amount,
            currency: item.type,
            reference: `Chest Reward`,
            action: 'in',
            category: item.type,
            credit_type: item.type,
            fe_display_remark: `宝箱奖励`,
            developer_remark: `宝箱ID: ${chest.id}`,
          }, { transaction });
        } catch (itemError) {
          console.error(`处理奖励项时出错: ${item.type}`, itemError);
          throw itemError;
        }
      }

      // 处理推广助力 - 给推荐人提供加速
      if (user.referrerId) {
        await processReferralBoost(userId, walletId, reward.level, transaction);
        console.log(`已处理推广助力，为推荐人 ${user.referrerId} 提供加速`);
      }

      // 累积宝箱池和Jackpot宝箱池 - 统一累积 0.0001 TON

      const jackpotResult = await contributeToJackpotPool(0.0001, userId, walletId, transaction);

      if (jackpotResult) {
        if (jackpotResult.success && jackpotResult.jackpotReward) {
          jackpotWinner = jackpotResult.jackpotReward;
        }
      }
    }

    // 检查并推进Jackpot宝箱池等级
    const activePool = await getActiveJackpotPool(transaction);
    if (activePool?.currentAmount && activePool?.targetAmount && activePool.currentAmount >= activePool.targetAmount) {
      await checkAndAdvanceJackpotLevel(activePool.level, userId,
        walletId, transaction);
      console.log(`Jackpot宝箱池已达到目标金额，已推进到下一级`);
    }

    // 统计各类型奖励总数
    const summary = {
      ticket: 0,
      fragment_green: 0,
      fragment_blue: 0,
      fragment_purple: 0,
      fragment_gold: 0,
      ton: 0,
      gem: 0
    };

    // 统计各等级宝箱数量
    const levelSummary = {
      level1: 0,
      level2: 0,
      level3: 0,
      level4: 0
    };

    allRewards.forEach(reward => {
      // 统计奖励
      reward.items.forEach(item => {
        if (summary.hasOwnProperty(item.type)) {
          //@ts-ignore
          summary[item.type] += item.amount;
        }
      });

      // 统计等级
      const levelKey = `level${reward.level}`;
      if (levelSummary.hasOwnProperty(levelKey)) {
        //@ts-ignore
        levelSummary[levelKey] += 1;
      }
    });

    // 在 processOpenChest 函数中，修改查询分享链接的部分
    // 获取高级宝箱的分享链接
    const shareLinks = [];
    if (highLevelChests.length > 0) {
    
    
      console.log(`处理 ${highLevelChests.length} 个高级宝箱的分享链接`);
  
      // 收集已经查询到的分享链接
      for (const chest of highLevelChests) {
        if (chest.shareLink) {
          shareLinks.push(chest.shareLink);
        }
      }
      
    
    // 如果没有直接查询到的分享链接，再尝试批量查询
  if (shareLinks.length === 0) {
    console.log('尝试批量查询分享链接');
    const links = await ShareBoostLink.findAll({
      where: {
        userId,
        chestId: highLevelChests.map(c => c.chestId)
      },
      transaction
    });
    
    console.log(`批量查询找到 ${links.length} 个分享链接`);
    
    for (const link of links) {
      shareLinks.push({
        chestId: link.chestId,
        chestLevel: link.chestLevel,
        shareCode: link.code,
        maxUses: link.maxUses,
        currentUses: link.currentUses,
        expiresAt: link.expiresAt
      });
    }
  }
    }

    return {
      openedCount: unopenedChests.length,
      chestIds,
      rewards: allRewards,
      summary,
      levelSummary,
      shareLinks: shareLinks.length > 0 ? shareLinks : [],
      jackpotWinner
    };
  } catch (error) {
    console.error('宝箱处理失败:', error);
    throw error;
  }
}


/**
 * 累积宝箱池和Jackpot宝箱池
 * @param amount 累积金额
 * @param transaction 事务对象
 */
async function contributeToJackpotPool(amount: number, userId: number, walletId: number, transaction: Transaction) {
  try {
    // 获取当前活跃的奖池
    const activePool = await getActiveJackpotPool(transaction);

    let jackpotReward = null;

    if (!activePool) {
      console.log('未找到活跃的Jackpot奖池，跳过贡献');
      return false;
    }

    // 检查开宝箱贡献是否已达到10 TON上限
    if (activePool.chestOpenAmount >= 10) {
      return false;
    }

    if (activePool.level !== 1) {
      // 计算实际可贡献金额
      let actualAmount = amount;
      if (new BigNumber(activePool.chestOpenAmount).plus(amount).isGreaterThan(10)) {
        actualAmount = new BigNumber(10).minus(activePool.chestOpenAmount).toNumber();
      }

      if (actualAmount > 0) {
        // 更新开宝箱贡献金额
        await JackpotPool.increment('chestOpenAmount', {
          by: actualAmount,
          where: { id: activePool.id },
          transaction
        });

        // 同时更新总金额
        await JackpotPool.increment('currentAmount', {
          by: actualAmount,
          where: { id: activePool.id },
          transaction
        });

        console.log(`Jackpot奖池 ${activePool.level} 级的贡献增加了 ${actualAmount}`);
      }
    }
    // 检查是否触发Jackpot奖励
    if (activePool.currentAmount >= activePool.targetAmount) {
      // 更新奖池信息
      const result = await JackpotPool.update({
        lastWinnerId: userId,
        lastWinnerWalletId: walletId,
        lastWinTime: new Date()
      }, {
        where: {
          id: activePool.id,
          lastWinnerId: { [Op.is]: null }
        },
        transaction
      });

      console.log('result', result);

      if (!result[0]) {
        throw new Error('更新Jackpot获奖者信息失败');
      }

      jackpotReward = {
        level: activePool.level,
        amount: 10,
        userId,
        walletId,
        poolId: activePool.id,
        winTime: new Date()
      };


      // 给用户发放奖励
      await UserWallet.increment('ton', {
        by: 10,
        where: { id: walletId },
        transaction
      });

      // 记录钱包历史
      await WalletHistory.create({
        userId,
        walletId,
        amount: 10,
        currency: 'ton',
        reference: 'Jackpot Pool Reward',
        action: 'in',
        category: 'jackpot',
        credit_type: 'ton',
        fe_display_remark: `Jackpot宝箱奖励`,
        developer_remark: `用户触发Jackpot奖励`,
      }, { transaction });

      // 创建全服公告
      await Announcement.create({
        userId,
        walletId,
        amount: 10,
        currency: 'ton',
        title: t('jackpot.announcement.winner.title'),
        content: t('jackpot.announcement.winner.content', {
          userId: String(userId),
          level: activePool.level,
          amount: 10
        }),
        type: 'jackpot_winner',
      }, { transaction });

      // 推进到下一级奖池
      await checkAndAdvanceJackpotLevel(activePool.level, userId, walletId, transaction);
    }



    return {
      success: true,
      jackpotReward
    };
  } catch (error) {
    console.error('累积Jackpot宝箱池失败:', error);
    throw error;
  }
}
