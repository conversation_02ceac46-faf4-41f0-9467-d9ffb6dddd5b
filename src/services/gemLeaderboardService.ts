// src/services/gemLeaderboardService.ts
import { sequelize } from "../config/db";
import { QueryTypes } from "sequelize";
import { UserWallet, User } from "../models";
import { createBigNumber } from '../utils/bigNumberConfig';

/**
 * 获取基于gem数量的排行榜
 * @param limit 限制返回的记录数量
 * @param offset 分页偏移量
 * @param walletId 当前用户钱包ID（可选）
 * @returns 排行榜数据和用户排名信息
 */
export async function getGemLeaderboard(limit: number = 100, offset: number = 0, walletId?: number) {
  try {
    // 查询gem排行榜数据
    const leaderboard = await sequelize.query(
      `SELECT 
        u.id as userId,
        uw.id as walletId, 
        uw.walletAddress,
        u.username, 
        u.firstName, 
        u.lastName, 
        u.photoUrl,
        uw.gem as gemAmount,
        @rank := @rank + 1 as \`rank\`
       FROM users u
       JOIN user_wallets uw ON u.id = uw.userId
       JOIN (SELECT @rank := 0) rank_init
       WHERE uw.gem > 0
       ORDER BY uw.gem DESC
       LIMIT :offset, :limit`,
      {
        replacements: { limit, offset },
        type: QueryTypes.SELECT,
      }
    );

    // 如果提供了用户ID，获取用户的排名
    let userRank = null;
    let userGemAmount = 0;

    if (walletId) {
      // 获取用户钱包信息
      const userWallet = await UserWallet.findOne({
        where: { id: walletId }
      });

      if (userWallet) {
        userGemAmount = Number(userWallet.gem || 0);

        // 获取用户排名
        const rankResult = await sequelize.query(
          `SELECT COUNT(*) + 1 as \`rank\`
           FROM user_wallets
           WHERE gem > :userGemAmount`,
          {
            replacements: { userGemAmount },
            type: QueryTypes.SELECT,
          }
        );

        //@ts-ignore
        userRank = rankResult.length > 0 ? rankResult[0].rank : null;
      }
    }

    // 获取总记录数，用于计算总页数
    const totalCountResult = await sequelize.query(
      `SELECT COUNT(*) as total FROM user_wallets WHERE gem > 0`,
      {
        type: QueryTypes.SELECT,
      }
    );
    
    //@ts-ignore
    const totalCount = totalCountResult[0].total || 0;
    
    return {
      leaderboard,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount
      },
      userInfo: walletId ? {
        rank: userRank,
        gemAmount: userGemAmount
      } : null
    };
  } catch (error) {
    console.error("获取gem排行榜失败:", error);
    throw error;
  }
}