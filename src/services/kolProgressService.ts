// src/services/kolProgressService.ts
import { sequelize } from "../config/db";
import { User, UserWallet, GameHistory, RewardClaim, WalletHistory } from "../models";
import { Op, QueryTypes } from "sequelize";
import dayjs from "dayjs";
import { t } from "../i18n";
import { KOL_POOLS } from "../jobs/personalKolRewardWorker";
import { TEAM_KOL_POOLS } from "../jobs/teamKolRewardWorker";
import { getUserDirectReferralGameAmount } from "../jobs/personalKolRewardWorker";
import { getTeamGameAmount } from "../jobs/teamKolRewardWorker";

/**
 * 获取用户KOL个人等级进度
 */
export async function getPersonalKolProgress(userId: number) {
  // 获取用户的一级直推游戏量
  const gameAmount = await getUserDirectReferralGameAmount(userId);

  // 确定当前等级和下一级别
  let currentLevel = null;
  let nextLevel = null;
  let currentLevelName = t("labels.notQualified");
  let nextLevelName = t("labels.oneStarKol");
  let progress = 0;
  let requiredAmount = 0;
  let remainingAmount = 0;

  if (gameAmount >= KOL_POOLS.three_star.minAmount) {
    // 已经是最高等级
    currentLevel = "three_star";
    currentLevelName = t("labels.threeStarKol");
    nextLevel = null;
    nextLevelName = t("labels.maxLevelReached");
    progress = 100;
    requiredAmount = KOL_POOLS.three_star.minAmount;
    remainingAmount = 0;
  } else if (gameAmount >= KOL_POOLS.two_star.minAmount) {
    currentLevel = "two_star";
    currentLevelName = t("labels.twoStarKol");
    nextLevel = "three_star";
    nextLevelName = t("labels.threeStarKol");
    progress = Math.floor((gameAmount - KOL_POOLS.two_star.minAmount) /
      (KOL_POOLS.three_star.minAmount - KOL_POOLS.two_star.minAmount) * 100);
    requiredAmount = KOL_POOLS.three_star.minAmount;
    remainingAmount = KOL_POOLS.three_star.minAmount - gameAmount;
  } else if (gameAmount >= KOL_POOLS.one_star.minAmount) {
    currentLevel = "one_star";
    currentLevelName = t("labels.oneStarKol");
    nextLevel = "two_star";
    nextLevelName = t("labels.twoStarKol");
    progress = Math.floor((gameAmount - KOL_POOLS.one_star.minAmount) /
      (KOL_POOLS.two_star.minAmount - KOL_POOLS.one_star.minAmount) * 100);
    requiredAmount = KOL_POOLS.two_star.minAmount;
    remainingAmount = KOL_POOLS.two_star.minAmount - gameAmount;
  } else {
    // 未达标
    currentLevel = null;
    nextLevel = "one_star";
    progress = Math.floor((gameAmount / KOL_POOLS.one_star.minAmount) * 100);
    requiredAmount = KOL_POOLS.one_star.minAmount;
    remainingAmount = KOL_POOLS.one_star.minAmount - gameAmount;
  }

  return {
    gameAmount,
    currentLevel,
    currentLevelName,
    nextLevel,
    nextLevelName,
    progress,
    requiredAmount,
    remainingAmount,
    levels: {
      one_star: KOL_POOLS.one_star.minAmount,
      two_star: KOL_POOLS.two_star.minAmount,
      three_star: KOL_POOLS.three_star.minAmount
    }
  };
}

/**
 * 获取用户KOL团队等级进度
 */
export async function getTeamKolProgress(userId: number) {
  // 获取用户的团队游戏量
  const gameAmount = await getTeamGameAmount(userId);

  // 确定当前等级和下一级别
  let currentLevel = null;
  let nextLevel = null;
  let currentLevelName = t("labels.notQualified");
  let nextLevelName = t("labels.silverKol");
  let progress = 0;
  let requiredAmount = 0;
  let remainingAmount = 0;

  if (gameAmount >= TEAM_KOL_POOLS.gold.minAmount) {
    // 已经是最高等级
    currentLevel = "gold";
    currentLevelName = t("labels.goldKol");
    nextLevel = null;
    nextLevelName = t("labels.maxLevelReached");
    progress = 100;
    requiredAmount = TEAM_KOL_POOLS.gold.minAmount;
    remainingAmount = 0;
  } else if (gameAmount >= TEAM_KOL_POOLS.silver.minAmount) {
    currentLevel = "silver";
    currentLevelName = t("labels.silverKol");
    nextLevel = "gold";
    nextLevelName = t("labels.goldKol");
    progress = Math.floor((gameAmount - TEAM_KOL_POOLS.silver.minAmount) /
      (TEAM_KOL_POOLS.gold.minAmount - TEAM_KOL_POOLS.silver.minAmount) * 100);
    requiredAmount = TEAM_KOL_POOLS.gold.minAmount;
    remainingAmount = TEAM_KOL_POOLS.gold.minAmount - gameAmount;
  } else {
    // 未达标
    currentLevel = null;
    nextLevel = "silver";
    progress = Math.floor((gameAmount / TEAM_KOL_POOLS.silver.minAmount) * 100);
    requiredAmount = TEAM_KOL_POOLS.silver.minAmount;
    remainingAmount = TEAM_KOL_POOLS.silver.minAmount - gameAmount;
  }

  return {
    gameAmount,
    currentLevel,
    currentLevelName,
    nextLevel,
    nextLevelName,
    progress,
    requiredAmount,
    remainingAmount,
    levels: {
      silver: TEAM_KOL_POOLS.silver.minAmount,
      gold: TEAM_KOL_POOLS.gold.minAmount
    }
  };
}

/**
 * 获取每日推广奖励进度
 */
export async function getDailyPromotionProgress(userId: number, walletId: number, expandLevel?: number) {
  // 获取今日的开始和结束时间
  const today = dayjs();
  const startOfDay = today.startOf('day').toDate();
  const endOfDay = today.endOf('day').toDate();

  // 获取用户今日推广的游戏量
  const todayReferralGames = await sequelize.query(
    `SELECT 
      COUNT(*) as gameCount,
      SUM(gh.betAmount) as totalBetAmount
     FROM users u
     JOIN user_wallets rw ON u.id = rw.userId
     JOIN game_histories gh ON rw.id = gh.walletId
     WHERE rw.id = :walletId
     AND gh.payout_status = 'done'
     AND gh.createdAt BETWEEN :startDate AND :endDate`,
    {
      replacements: { walletId, startDate: startOfDay, endDate: endOfDay },
      type: QueryTypes.SELECT,
    }
  );

  // 获取用户总的推广的游戏量
  const totalReferralGames = await sequelize.query(
    `SELECT COUNT(*) as gameCount, SUM(gh.betAmount) as totalBetAmount FROM users u JOIN user_wallets rw ON rw.userId = u.id JOIN game_histories gh ON rw.id = gh.walletId WHERE rw.id = :walletId AND gh.payout_status = 'done'`,
    {
      replacements: { walletId },
      type: QueryTypes.SELECT,
    }
  );


  // @ts-ignore
  const todayBetAmount = todayReferralGames[0]?.totalBetAmount || 0;
  // @ts-ignore
  const totalBetAmount = totalReferralGames[0]?.totalBetAmount || 0;
  // @ts-ignore
  const todayGameCount = todayReferralGames[0]?.gameCount || 0;

  // @ts-ignore
  const totalGameCount = totalReferralGames[0]?.gameCount || 0;


  // 获取展开层级的详细用户信息
  let expandedLevelUsers: object[] = [];

  expandedLevelUsers = await getDownlineLevelUsers(walletId, 1, startOfDay, endOfDay);


  return {
    todayGameCount,
    totalGameCount,
    todayBetAmount,
    totalBetAmount,
    expandedLevelUsers
  };
}

/**
 * 获取指定层级的下线用户详细信息
 * @param userId 用户ID
 * @param level 层级
 * @param startDate 开始日期（用于计算今日游戏量）
搞 * @param endDate 结束日期（用于计算今日游戏量）
 */async function getDownlineLevelUsers(walletId: number, level: number, startDate: Date, endDate: Date) {
  // 查询直接下级用户的详细信息
  const levelUsers = await sequelize.query(
    `WITH direct_downline AS (
      SELECT 
        u2.id as member_id
      FROM user_wallets uw1
      JOIN users u1 ON uw1.userId = u1.id
      JOIN users u2 ON u2.referrerId = u1.id
      WHERE uw1.id = :walletId
    ),
    game_stats AS (
      SELECT 
        gh.walletId,
        COUNT(*) as total_games,
        SUM(gh.betAmount) as total_bet,
        COUNT(CASE WHEN gh.createdAt BETWEEN :startDate AND :endDate THEN 1 END) as today_games,
        SUM(CASE WHEN gh.createdAt BETWEEN :startDate AND :endDate THEN gh.betAmount ELSE 0 END) as today_bet
      FROM game_histories gh
      GROUP BY gh.walletId
    )
    SELECT 
      u.id as userId,
      u.username,
      u.photoUrl,
      u.createdAt as registerTime,
      uw.id as walletId,
      uw.walletAddress,
      COALESCE(gs.total_games, 0) as totalGameCount,
      COALESCE(gs.total_bet, 0) as totalBetAmount,
      COALESCE(gs.today_games, 0) as todayGameCount,
      COALESCE(gs.today_bet, 0) as todayBetAmount
    FROM direct_downline d
    JOIN users u ON d.member_id = u.id
    JOIN user_wallets uw ON u.id = uw.userId
    LEFT JOIN game_stats gs ON gs.walletId = uw.id
    ORDER BY COALESCE(gs.total_bet, 0) DESC`,
    {
      replacements: {
        walletId,
        startDate,
        endDate
      },
      type: QueryTypes.SELECT,
    }
  );

  return levelUsers;
}

/**
 * 获取用户个人KOL推广统计数据
 * @param userId 用户ID
 */
export async function getPersonalKolStats(userId: number) {
  // 获取今日的开始和结束时间
  const today = dayjs();
  const startOfDay = today.startOf('day').toDate();
  const endOfDay = today.endOf('day').toDate();

  // 查询用户的直推人数、金牛次数、游戏金额和直推用户的金牛变身次数
  const directReferralStats = await sequelize.query(
    `SELECT 
      COUNT(DISTINCT r.id) as directReferralCount,
      COUNT(DISTINCT gh.id) as directGameCount,
      SUM(IFNULL(gh.betAmount, 0)) as directBetAmount,
      SUM(IFNULL(us.goldenBullCount, 0)) as directReferralBullCount
     FROM users u
     LEFT JOIN users r ON r.referrerId = u.id
     LEFT JOIN user_wallets rw ON r.id = rw.userId
     LEFT JOIN game_histories gh ON rw.id = gh.walletId
     LEFT JOIN user_status_snapshots us ON r.id = us.userId
     WHERE u.id = :userId`,
    {
      replacements: { userId },
      type: QueryTypes.SELECT,
    }
  );

  // 查询用户今日的直推人数、金牛次数和游戏金额
  const todayDirectReferralStats = await sequelize.query(
    `SELECT 
      COUNT(DISTINCT r.id) as todayDirectReferralCount,
      COUNT(DISTINCT gh.id) as todayDirectGameCount,
      SUM(IFNULL(gh.betAmount, 0)) as todayDirectBetAmount
     FROM users u
     LEFT JOIN users r ON r.referrerId = u.id
     LEFT JOIN user_wallets rw ON r.id = rw.userId
     LEFT JOIN game_histories gh ON rw.id = gh.walletId AND gh.createdAt BETWEEN :startDate AND :endDate
     WHERE u.id = :userId`,
    {
      replacements: { userId, startDate: startOfDay, endDate: endOfDay },
      type: QueryTypes.SELECT,
    }
  );

  // 获取用户的KOL等级信息
  const kolProgress = await getPersonalKolProgress(userId);

  return {
    // 总计数据
    // @ts-ignore
    directReferralCount: directReferralStats[0]?.directReferralCount || 0,
    // @ts-ignore
    directGameCount: directReferralStats[0]?.directGameCount || 0,
    // @ts-ignore
    directBetAmount: directReferralStats[0]?.directBetAmount || 0,
    // @ts-ignore
    directReferralBullCount: directReferralStats[0]?.directReferralBullCount || 0,

    // 今日数据
    // @ts-ignore
    todayDirectReferralCount: todayDirectReferralStats[0]?.todayDirectReferralCount || 0,
    // @ts-ignore
    todayDirectGameCount: todayDirectReferralStats[0]?.todayDirectGameCount || 0,
    // @ts-ignore
    todayDirectBetAmount: todayDirectReferralStats[0]?.todayDirectBetAmount || 0,

    // KOL等级信息
    kolLevel: kolProgress.currentLevelName,
    kolProgress: kolProgress.progress,
    nextKolLevel: kolProgress.nextLevelName,
    remainingAmount: kolProgress.remainingAmount
  };
}

/**
 * 获取每日推广奖励进度
 * @param userId 用户ID
 * @param walletId 钱包ID
 */
export async function getDailyPromotionProgress2(userId: number, walletId: number) {
  // 获取今日的开始和结束时间
  const today = dayjs();
  const startOfDay = today.startOf('day').toDate();
  const endOfDay = today.endOf('day').toDate();

  // 获取用户今日推广的游戏量
  const todayReferralGames = await sequelize.query(
    `SELECT 
      COUNT(*) as gameCount,
      SUM(gh.betAmount) as totalBetAmount
     FROM users u
     JOIN user_wallets rw ON u.id = rw.userId
     JOIN game_histories gh ON rw.id = gh.walletId
     WHERE rw.id = :walletId
     AND gh.createdAt BETWEEN :startDate AND :endDate and gh.payout_status = 'done'`,
    {
      replacements: { walletId, startDate: startOfDay, endDate: endOfDay },
      type: QueryTypes.SELECT,
    }
  );

  // 门票返利比例配置
  const REFERRAL_RATES = {
    level1: 0.10, // 10%
    level2: 0.10, // 10%
    level3: 0.05, // 5%
    level4: 0.03, // 3%
    level5: 0.02  // 2%
  };

  // 获取用户的推广奖励配置
  const PROMOTION_REWARD_RATE = 100; // 每100 USD游戏量奖励1张门票
  const MIN_REQUIRED_AMOUNT = 300; // 最低需要300 USD游戏量才能领取奖励

  // @ts-ignore
  const totalBetAmount = todayReferralGames[0]?.totalBetAmount || 0;
  // @ts-ignore
  const gameCount = todayReferralGames[0]?.gameCount || 0;

  // 获取5层下线的游戏量数据
  const downlineStats = await getDownlineGameStats(userId, 5, startOfDay, endOfDay);

  // 计算5层下线的返利奖励
  let referralRewards = 0;
  let levelRewards = [];

  for (let i = 0; i < downlineStats.length; i++) {
    const level = i + 1;
    //@ts-ignore
    const levelRate = REFERRAL_RATES[`level${level}`];
    const levelBetAmount = downlineStats[i].totalBetAmount || 0;

    // 计算该层级的门票数量
    const levelTickets = Math.floor(levelBetAmount / PROMOTION_REWARD_RATE);

    // 计算该层级的返利奖励
    const levelReward = Math.floor(levelTickets * levelRate);
    referralRewards += levelReward;

    levelRewards.push({
      level,
      betAmount: levelBetAmount,
      tickets: levelTickets,
      rate: levelRate * 100,
      reward: levelReward
    });
  }

  // 总预估奖励 = 返利奖励
  const estimatedReward = referralRewards;

  // 计算进度
  const progress = Math.min(100, Math.floor((totalBetAmount / MIN_REQUIRED_AMOUNT) * 100));


  return {
    gameCount,
    totalBetAmount,
    referralRewards,
    estimatedReward,
    progress,
    // canClaim: totalBetAmount >= MIN_REQUIRED_AMOUNT,
    // alreadyClaimed: !!todayRewardClaimed,
    minRequiredAmount: MIN_REQUIRED_AMOUNT,
    rewardRate: PROMOTION_REWARD_RATE,
    remainingAmount: Math.max(0, MIN_REQUIRED_AMOUNT - totalBetAmount),
    referralRates: REFERRAL_RATES,
    levelRewards,
    downlineStats
  };
}

/**
 * 获取用户团队KOL推广统计数据
 * @param userId 用户ID
 */
export async function getTeamKolStats(userId: number) {
  // 获取今日的开始和结束时间
  const today = dayjs();
  const startOfDay = today.startOf('day').toDate();
  const endOfDay = today.endOf('day').toDate();

  // 获取本周的开始和结束时间
  const startOfWeek = today.startOf('week').toDate();
  const endOfWeek = today.endOf('week').toDate();

  // 查询用户的团队五代推荐人数和游戏金额（总计）
  const teamStats = await sequelize.query(
    `WITH RECURSIVE team_hierarchy AS (
      -- 基础查询：获取直接下级
      SELECT 
        u.id as root_id,
        r.id as member_id,
        1 as level
      FROM users u
      JOIN users r ON r.referrerId = u.id
      WHERE u.id = :userId
      
      UNION ALL
      
      -- 递归查询：获取更深层级的下级
      SELECT 
        th.root_id,
        u.id as member_id,
        th.level + 1
      FROM team_hierarchy th
      JOIN users u ON u.referrerId = th.member_id
      WHERE th.level < 5
    )
    SELECT 
      COUNT(DISTINCT th.member_id) as teamMemberCount,
      SUM(IFNULL(gh.betAmount, 0)) as teamBetAmount
    FROM team_hierarchy th
    LEFT JOIN user_wallets uw ON th.member_id = uw.userId
    LEFT JOIN game_histories gh ON uw.id = gh.walletId`,
    {
      replacements: { userId },
      type: QueryTypes.SELECT,
    }
  );

  // 查询用户今日的团队五代推荐人数和游戏金额
  const todayTeamStats = await sequelize.query(
    `WITH RECURSIVE team_hierarchy AS (
      -- 基础查询：获取直接下级
      SELECT 
        u.id as root_id,
        r.id as member_id,
        1 as level
      FROM users u
      JOIN users r ON r.referrerId = u.id
      WHERE u.id = :userId
      
      UNION ALL
      
      -- 递归查询：获取更深层级的下级
      SELECT 
        th.root_id,
        u.id as member_id,
        th.level + 1
      FROM team_hierarchy th
      JOIN users u ON u.referrerId = th.member_id
      WHERE th.level < 5
    )
    SELECT 
      COUNT(DISTINCT th.member_id) as todayTeamMemberCount,
      SUM(IFNULL(gh.betAmount, 0)) as todayTeamBetAmount
    FROM team_hierarchy th
    LEFT JOIN user_wallets uw ON th.member_id = uw.userId
    LEFT JOIN game_histories gh ON uw.id = gh.walletId AND gh.createdAt BETWEEN :startDate AND :endDate`,
    {
      replacements: { userId, startDate: startOfDay, endDate: endOfDay },
      type: QueryTypes.SELECT,
    }
  );

  // 查询用户本周的团队五代推荐人数和游戏金额
  const weekTeamStats = await sequelize.query(
    `WITH RECURSIVE team_hierarchy AS (
      -- 基础查询：获取直接下级
      SELECT 
        u.id as root_id,
        r.id as member_id,
        1 as level
      FROM users u
      JOIN users r ON r.referrerId = u.id
      WHERE u.id = :userId
      
      UNION ALL
      
      -- 递归查询：获取更深层级的下级
      SELECT 
        th.root_id,
        u.id as member_id,
        th.level + 1
      FROM team_hierarchy th
      JOIN users u ON u.referrerId = th.member_id
      WHERE th.level < 5
    )
    SELECT 
      COUNT(DISTINCT th.member_id) as weekTeamMemberCount,
      SUM(IFNULL(gh.betAmount, 0)) as weekTeamBetAmount
    FROM team_hierarchy th
    LEFT JOIN user_wallets uw ON th.member_id = uw.userId
    LEFT JOIN game_histories gh ON uw.id = gh.walletId AND gh.createdAt BETWEEN :startDate AND :endDate`,
    {
      replacements: { userId, startDate: startOfWeek, endDate: endOfWeek },
      type: QueryTypes.SELECT,
    }
  );

  // 获取用户的团队KOL等级信息
  const kolProgress = await getTeamKolProgress(userId);

  // 获取团队成员按级别分布
  const teamLevelDistribution = await sequelize.query(
    `WITH RECURSIVE team_hierarchy AS (
      -- 基础查询：获取直接下级
      SELECT 
        u.id as root_id,
        r.id as member_id,
        1 as level
      FROM users u
      JOIN users r ON r.referrerId = u.id
      WHERE u.id = :userId
      
      UNION ALL
      
      -- 递归查询：获取更深层级的下级
      SELECT 
        th.root_id,
        u.id as member_id,
        th.level + 1
      FROM team_hierarchy th
      JOIN users u ON u.referrerId = th.member_id
      WHERE th.level < 5
    )
    SELECT 
      th.level,
      COUNT(DISTINCT th.member_id) as memberCount,
      SUM(IFNULL(gh.betAmount, 0)) as betAmount,
      SUM(IFNULL(uss.goldenBullCount, 0)) as bullCount
    FROM team_hierarchy th
    LEFT JOIN user_wallets uw ON th.member_id = uw.userId
    LEFT JOIN game_histories gh ON uw.id = gh.walletId
    LEFT JOIN user_status_snapshots uss ON th.member_id = uss.userId
    GROUP BY th.level
    ORDER BY th.level`,
    {
      replacements: { userId },
      type: QueryTypes.SELECT,
    }
  );

  // 获取团队总的金牛变身次数
  const teamBullCount = await sequelize.query(
    `WITH RECURSIVE team_hierarchy AS (
      -- 基础查询：获取直接下级
      SELECT 
        u.id as root_id,
        r.id as member_id,
        1 as level
      FROM users u
      JOIN users r ON r.referrerId = u.id
      WHERE u.id = :userId
      
      UNION ALL
      
      -- 递归查询：获取更深层级的下级
      SELECT 
        th.root_id,
        u.id as member_id,
        th.level + 1
      FROM team_hierarchy th
      JOIN users u ON u.referrerId = th.member_id
      WHERE th.level < 5
    )
    SELECT 
      SUM(IFNULL(uss.goldenBullCount, 0)) as totalBullCount
    FROM team_hierarchy th
    LEFT JOIN user_status_snapshots uss ON th.member_id = uss.userId`,
    {
      replacements: { userId },
      type: QueryTypes.SELECT,
    }
  );

  return {
    // 总计数据
    // @ts-ignore
    teamMemberCount: teamStats[0]?.teamMemberCount || 0,
    // @ts-ignore
    teamBetAmount: teamStats[0]?.teamBetAmount || 0,
    // @ts-ignore
    teamBullCount: teamBullCount[0]?.totalBullCount || 0,

    // 今日数据
    // @ts-ignore
    todayTeamMemberCount: todayTeamStats[0]?.todayTeamMemberCount || 0,
    // @ts-ignore
    todayTeamBetAmount: todayTeamStats[0]?.todayTeamBetAmount || 0,

    // 本周数据
    // @ts-ignore
    weekTeamMemberCount: weekTeamStats[0]?.weekTeamMemberCount || 0,
    // @ts-ignore
    weekTeamBetAmount: weekTeamStats[0]?.weekTeamBetAmount || 0,

    // 团队成员按级别分布
    teamLevelDistribution,

    // KOL等级信息
    kolLevel: kolProgress.currentLevelName,
    kolProgress: kolProgress.progress,
    nextKolLevel: kolProgress.nextLevelName,
    remainingAmount: kolProgress.remainingAmount
  };
}

/**
 * 获取用户下线各层级的游戏统计数据
 * @param userId 用户ID
 * @param maxLevel 最大层级数
 * @param startDate 开始日期
 * @param endDate 结束日期
 */
async function getDownlineGameStats(userId: number, maxLevel: number = 5, startDate: Date, endDate: Date) {
  const result = [];

  for (let level = 1; level <= maxLevel; level++) {
    // 查询特定层级的下线游戏数据
    const levelStats = await sequelize.query(
      `WITH RECURSIVE downline AS (
        -- 基础查询：获取直接下级
        SELECT 
          u.id as root_id,
          r.id as member_id,
          1 as level
        FROM users u
        JOIN users r ON r.referrerId = u.id
        WHERE u.id = :userId
        
        UNION ALL
        
        -- 递归查询：获取更深层级的下级
        SELECT 
          d.root_id,
          u.id as member_id,
          d.level + 1
        FROM downline d
        JOIN users u ON u.referrerId = d.member_id
        WHERE d.level < :currentLevel
      )
      SELECT 
        COUNT(DISTINCT d.member_id) as userCount,
        COUNT(DISTINCT gh.id) as gameCount,
        SUM(IFNULL(gh.betAmount, 0)) as totalBetAmount
      FROM downline d
      LEFT JOIN user_wallets uw ON d.member_id = uw.userId
      LEFT JOIN game_histories gh ON uw.id = gh.walletId 
        AND gh.createdAt BETWEEN :startDate AND :endDate
      WHERE d.level = :currentLevel`,
      {
        replacements: {
          userId,
          currentLevel: level,
          startDate,
          endDate
        },
        type: QueryTypes.SELECT,
      }
    );

    result.push({
      level,
      // @ts-ignore
      userCount: levelStats[0]?.userCount || 0,
      // @ts-ignore
      gameCount: levelStats[0]?.gameCount || 0,
      // @ts-ignore
      totalBetAmount: levelStats[0]?.totalBetAmount || 0
    });
  }

  return result;
}

/**
 * 获取用户游戏统计和推荐收益数据
 */
export async function getUserGameStats(userId: number, walletId: number) {
  const today = dayjs();
  const startOfDay = today.startOf('day').toDate();
  const endOfDay = today.endOf('day').toDate();

  // 1. 查询用户游戏次数
  // const gameStats = await GameHistory.findAll({
  //   where: {
  //     walletId,
  //     payout_status: 'done'
  //   },
  //   attributes: [
  //     [sequelize.fn('COUNT', sequelize.col('id')), 'totalGames'],
  //     [sequelize.fn('SUM', sequelize.col('betAmount')), 'totalBetAmount']
  //   ],
  //   raw: true
  // });

  // 2. 查询用户累积推荐收益
  const totalReferralEarnings = await WalletHistory.findAll({
    where: {
      walletId,
      action: 'in',
      reference: 'Claimed Ticket Rebate'
    },
    attributes: [
      [sequelize.fn('SUM', sequelize.col('amount')), 'totalEarnings']
    ],
    raw: true
  });

  // 3. 获取今日预估推荐收益
  const dailyPromotionStats = await getDailyPromotionProgress2(userId, walletId);


  return {
    earnings: {
      //@ts-ignore
      totalReferralEarnings: Number(totalReferralEarnings[0]?.totalEarnings || 0),
      estimatedTodayEarnings: dailyPromotionStats.estimatedReward || 0
    }
  };
}