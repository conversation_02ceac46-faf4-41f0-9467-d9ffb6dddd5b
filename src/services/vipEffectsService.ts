// src/services/vipEffectsService.ts
import { VipMembership } from '../models/VipMembership';

/**
 * VIP效果接口
 */
export interface VipEffects {
  isVip: boolean;
  deliverySpeedMultiplier: number;  // 出货线速度加成倍率
  blockPriceMultiplier: number;     // 出货线价格加成倍率
  productionSpeedMultiplier: number; // 牧场区生产速度加成倍率
}

/**
 * VIP效果服务类
 * 负责计算和提供VIP会员的各种游戏效果加成
 */
export class VipEffectsService {
  /**
   * 获取用户的VIP效果
   * @param walletId 用户钱包ID
   * @returns VIP效果对象
   */
  public static async getVipEffects(walletId: number): Promise<VipEffects> {
    try {
      const vipMembership = await VipMembership.findOne({
        where: { walletId }
      });

      // 检查VIP会员状态是否有效
      if (!vipMembership || !vipMembership.checkAndUpdateStatus()) {
        return {
          isVip: false,
          deliverySpeedMultiplier: 1,
          blockPriceMultiplier: 1,
          productionSpeedMultiplier: 1
        };
      }

      // 返回VIP效果加成
      return {
        isVip: true,
        deliverySpeedMultiplier: 1.3, // VIP 30% 出货线速度加成
        blockPriceMultiplier: 1.2,    // VIP 20% 出货线价格加成
        productionSpeedMultiplier: 1.3 // VIP 30% 牧场区生产速度加成
      };
    } catch (error) {
      console.error('Get VIP effects error:', error);
      // 发生错误时返回默认值（无VIP效果）
      return {
        isVip: false,
        deliverySpeedMultiplier: 1,
        blockPriceMultiplier: 1,
        productionSpeedMultiplier: 1
      };
    }
  }

  /**
   * 检查用户是否为VIP会员
   * @param walletId 用户钱包ID
   * @returns 是否为VIP会员
   */
  public static async isVipMember(walletId: number): Promise<boolean> {
    try {
      const vipMembership = await VipMembership.findOne({
        where: { walletId }
      });

      return vipMembership ? vipMembership.checkAndUpdateStatus() : false;
    } catch (error) {
      console.error('Check VIP membership error:', error);
      return false;
    }
  }

  /**
   * 获取VIP会员信息
   * @param walletId 用户钱包ID
   * @returns VIP会员信息或null
   */
  public static async getVipMembership(walletId: number): Promise<VipMembership | null> {
    try {
      const vipMembership = await VipMembership.findOne({
        where: { walletId }
      });

      if (vipMembership && vipMembership.checkAndUpdateStatus()) {
        return vipMembership;
      }

      return null;
    } catch (error) {
      console.error('Get VIP membership error:', error);
      return null;
    }
  }
}

export default VipEffectsService;
