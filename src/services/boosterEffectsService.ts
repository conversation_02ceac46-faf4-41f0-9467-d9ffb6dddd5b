// src/services/boosterEffectsService.ts
import { ActiveBooster } from '../models/ActiveBooster';
import { Op } from 'sequelize';
import dayjs from 'dayjs';

/**
 * 加速器效果接口
 */
export interface BoosterEffects {
  speedMultiplier: number;  // 速度加成倍率
  hasTimeWarp: boolean;     // 是否有时间跳跃效果
}

/**
 * 加速器效果服务类
 * 负责计算和提供各种加速器道具的游戏效果
 */
export class BoosterEffectsService {
  /**
   * 获取用户当前生效的加速器效果
   * @param walletId 用户钱包ID
   * @returns 加速器效果对象
   */
  public static async getBoosterEffects(walletId: number): Promise<BoosterEffects> {
    try {
      // 查询当前生效的加速器
      const activeBoosters = await ActiveBooster.findAll({
        where: {
          walletId,
          endTime: { [Op.gt]: dayjs().toDate() }
        }
      });

      const effects: BoosterEffects = {
        speedMultiplier: 1,
        hasTimeWarp: false
      };

      // 处理每个生效的加速器
      for (const booster of activeBoosters) {
        if (booster.type === 'speed_boost') {
          // 速度加成取最大值（多个速度加成不叠加，取最高的）
          effects.speedMultiplier = Math.max(effects.speedMultiplier, booster.multiplier);
        } else if (booster.type === 'time_warp') {
          // 时间跳跃效果（有任何一个时间跳跃道具就为true）
          effects.hasTimeWarp = true;
        }
      }

      return effects;
    } catch (error) {
      console.error('Get booster effects error:', error);
      // 发生错误时返回默认值（无加速器效果）
      return { speedMultiplier: 1, hasTimeWarp: false };
    }
  }

  /**
   * 获取用户当前生效的速度加成倍率
   * @param walletId 用户钱包ID
   * @returns 速度加成倍率
   */
  public static async getSpeedMultiplier(walletId: number): Promise<number> {
    try {
      const effects = await this.getBoosterEffects(walletId);
      return effects.speedMultiplier;
    } catch (error) {
      console.error('Get speed multiplier error:', error);
      return 1;
    }
  }

  /**
   * 检查用户是否有时间跳跃效果
   * @param walletId 用户钱包ID
   * @returns 是否有时间跳跃效果
   */
  public static async hasTimeWarpEffect(walletId: number): Promise<boolean> {
    try {
      const effects = await this.getBoosterEffects(walletId);
      return effects.hasTimeWarp;
    } catch (error) {
      console.error('Check time warp effect error:', error);
      return false;
    }
  }

  /**
   * 获取用户当前生效的加速器列表
   * @param walletId 用户钱包ID
   * @returns 生效的加速器列表
   */
  public static async getActiveBoosters(walletId: number): Promise<ActiveBooster[]> {
    try {
      return await ActiveBooster.findAll({
        where: {
          walletId,
          endTime: { [Op.gt]: dayjs().toDate() }
        },
        order: [['endTime', 'ASC']]
      });
    } catch (error) {
      console.error('Get active boosters error:', error);
      return [];
    }
  }

  /**
   * 检查特定类型的加速器是否生效
   * @param walletId 用户钱包ID
   * @param boosterType 加速器类型
   * @returns 是否有该类型的加速器生效
   */
  public static async hasActiveBoosterOfType(walletId: number, boosterType: string): Promise<boolean> {
    try {
      const count = await ActiveBooster.count({
        where: {
          walletId,
          type: boosterType,
          endTime: { [Op.gt]: dayjs().toDate() }
        }
      });
      return count > 0;
    } catch (error) {
      console.error('Check active booster of type error:', error);
      return false;
    }
  }
}

export default BoosterEffectsService;
