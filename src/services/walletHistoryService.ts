// src/services/walletHistoryService.ts
import { WalletHistory } from "../models/WalletHistory";
import { Op } from "sequelize";
import { Transaction } from "sequelize";
import { t } from "../i18n";

/**
 * 创建钱包历史记录（用于游戏预约）
 * @param params 钱包历史记录参数
 */
export async function createWalletHistory(params: {
  userId: number;
  walletId: number;
  amount: number;
  currency: string;
  sessionId: number;
  session_category: string;
  roundIndex: number;
  transaction: Transaction;
}) {
  return await WalletHistory.create({
    userId: params.userId,
    walletId: params.walletId,
    amount: params.amount,
    currency: params.currency,
    reference: t('walletHistory.bet'),
    action: "out",
    category: "usd",
    credit_type: "usd",
    fe_display_remark: t('walletHistory.bet'),
    developer_remark: t('walletHistory.betSession', { sessionNumber: params.session_category === "12:00:00" ? 1 : 2, roundIndex: params.roundIndex }),
  }, { transaction: params.transaction });
}

/**
 * 记录钱包历史(流水)
 * @param userId 用户ID
 * @param walletId 钱包ID
 * @param currency 货币类型: "COIN","TICKET","TON",...
 * @param amount 
 * @param reference 参考说明(如"购买票","存款","幸运抽奖",...)
 * @param action 动作: "in"或"out"
 * @param category 类别: "DEPOSIT","WITHDRAW","PURCHASE",...
 * @param credit_type 信用类型: "CREDIT","DEBIT"
 * @param fe_display_remark 前端展示备注
 * @param developer_remark 开发者备注
 * @param transaction 可选的事务对象
 */
export async function recordWalletHistory(
  userId: number,
  walletId: number,
  currency: string,
  amount: number,
  reference: string,
  action: string,
  category: string,
  credit_type: string,
  fe_display_remark: string,
  developer_remark: string,
  transaction?: Transaction
) {
  return await WalletHistory.create({
    userId,
    walletId,
    currency,
    amount,
    reference,
    action:action,
    credit_type:credit_type,
    fe_display_remark:fe_display_remark,
    category:category,
    developer_remark:developer_remark,
  }, transaction ? { transaction } : undefined);
}

/**
 * 查询用户钱包历史记录
 * @param userId 用户ID
 * @param walletId 钱包ID
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @param currency 货币类型(可选)
 * @param action 动作类型(可选)
 * @param category 类别(可选)
 */
export async function queryWalletHistory(
  userId: number,
  walletId: number,
  startDate: Date,
  endDate: Date,
  currency?: string,
  action?: string,
  category?: string
) {
  const whereClause: any = {
    userId,
    walletId,
    createdAt: {
      [Op.between]: [startDate, endDate]
    }
  };

  if (currency) whereClause.currency = currency;
  if (action) whereClause.action = action;
  if (category) whereClause.category = category;

  return await WalletHistory.findAll({
    where: whereClause,
    order: [["createdAt", "DESC"]]
  });
}

/**
 * 统计用户钱包流水总额
 * @param userId 用户ID
 * @param walletId 钱包ID
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @param currency 货币类型
 * @param action 动作类型(可选)
 */
export async function sumWalletHistory(
  userId: number,
  walletId: number,
  startDate: Date,
  endDate: Date,
  currency: string,
  action?: string
) {
  const whereClause: any = {
    userId,
    walletId,
    currency,
    createdAt: {
      [Op.between]: [startDate, endDate]
    }
  };

  if (action) whereClause.action = action;

  const result = await WalletHistory.sum("amount", {
    where: whereClause
  });

  return result || 0;
}
