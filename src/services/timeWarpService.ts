import { FarmPlot } from '../models/FarmPlot';
import { DeliveryLine } from '../models/DeliveryLine';
import { UserWallet } from '../models/UserWallet';
import { VipMembership } from '../models/VipMembership';
import { ActiveBooster } from '../models/ActiveBooster';
import { TimeWarpHistory } from '../models/TimeWarpHistory';
import { createBigNumber, formatToThreeDecimalsNumber } from '../utils/bigNumberConfig';
import { Op } from 'sequelize';
import dayjs from 'dayjs';

export interface TimeWarpResult {
  gemsEarned: number;
  milkProduced: number;
  milkProcessed: number;
  farmProductionPerSecond: number;
  deliveryProcessingPerSecond: number;
  hasVip: boolean;
  hasSpeedBoost: boolean;
  speedBoostMultiplier: number;
}

export class TimeWarpService {
  /**
   * 计算时间跳跃收益
   * @param walletId 用户钱包ID
   * @param hours 跳跃小时数
   * @returns 时间跳跃收益详情
   */
  static async calculateTimeWarpRewards(walletId: number, hours: number): Promise<TimeWarpResult> {
    // 获取用户的农场区块
    const farmPlots = await FarmPlot.findAll({
      where: { walletId, isUnlocked: true }
    });

    // 获取用户的出货线
    const deliveryLine = await DeliveryLine.findOne({
      where: { walletId }
    });

    if (!deliveryLine) {
      throw new Error('用户出货线不存在');
    }

    // 检查VIP状态
    const vipMembership = await VipMembership.findOne({
      where: {
        walletId,
        isActive: true,
        endTime: { [Op.gt]: dayjs().toDate() }
      }
    });
    const hasVip = !!vipMembership;

    // 检查速度加成状态
    const activeSpeedBoost = await ActiveBooster.findOne({
      where: {
        walletId,
        type: 'speed_boost',
        status: 'active',
        endTime: { [Op.gt]: dayjs().toDate() }
      }
    });
    const hasSpeedBoost = !!activeSpeedBoost;
    const speedBoostMultiplier = hasSpeedBoost ? (activeSpeedBoost?.multiplier || 1) : 1;

    // 计算农场区总产量（每秒）
    let farmProductionPerSecond = 0;
    for (const plot of farmPlots) {
      let plotProductionPerSecond = plot.calculateTotalProductionPerSecond();
      
      // 应用VIP加成（+30%农场生产速度）
      if (hasVip) {
        plotProductionPerSecond *= 1.3;
      }
      
      farmProductionPerSecond += plotProductionPerSecond;
    }

    // 计算出货线处理能力（每秒）
    let deliveryProcessingPerSecond = deliveryLine.blockUnit / deliveryLine.deliverySpeed;
    
    // 应用VIP加成（+30%出货速度）
    if (hasVip) {
      deliveryProcessingPerSecond *= 1.3;
    }
    
    // 应用速度加成
    if (hasSpeedBoost) {
      // 速度加成公式：实际速度 = 基础速度 × 倍数
      deliveryProcessingPerSecond *= speedBoostMultiplier;
    }

    // 计算时间跳跃期间的收益
    const timeInSeconds = hours * 3600;
    
    // 时间内生产的牛奶
    const milkProduced = farmProductionPerSecond * timeInSeconds;
    
    // 时间内可处理的牛奶
    const milkCanProcess = deliveryProcessingPerSecond * timeInSeconds;
    
    // 实际处理的牛奶（取较小值）
    const milkProcessed = Math.min(milkProduced, milkCanProcess);
    
    // 计算获得的GEM
    let gemsEarned = 0;
    if (milkProcessed > 0) {
      // 计算可以制作的方块数量
      const blocksProduced = Math.floor(milkProcessed / deliveryLine.blockUnit);
      
      // 计算方块价格（应用VIP加成 +20%方块价格）
      let blockPrice = deliveryLine.blockPrice;
      if (hasVip) {
        blockPrice *= 1.2;
      }
      
      // 计算总GEM收益
      gemsEarned = blocksProduced * blockPrice;
    }

    return {
      gemsEarned: formatToThreeDecimalsNumber(createBigNumber(gemsEarned)),
      milkProduced: formatToThreeDecimalsNumber(createBigNumber(milkProduced)),
      milkProcessed: formatToThreeDecimalsNumber(createBigNumber(milkProcessed)),
      farmProductionPerSecond: formatToThreeDecimalsNumber(createBigNumber(farmProductionPerSecond)),
      deliveryProcessingPerSecond: formatToThreeDecimalsNumber(createBigNumber(deliveryProcessingPerSecond)),
      hasVip,
      hasSpeedBoost,
      speedBoostMultiplier
    };
  }

  /**
   * 执行时间跳跃，直接给用户添加GEM收益
   * @param walletId 用户钱包ID
   * @param hours 跳跃小时数
   * @param transaction 数据库事务
   * @param productId 商品ID（可选）
   * @param purchaseId 购买记录ID（可选）
   * @returns 时间跳跃结果
   */
  static async executeTimeWarp(walletId: number, hours: number, transaction?: any, productId?: number, purchaseId?: number): Promise<TimeWarpResult> {
    // 计算收益
    const rewards = await this.calculateTimeWarpRewards(walletId, hours);
    
    if (rewards.gemsEarned > 0) {
      // 直接给用户添加GEM
      await UserWallet.increment(
        { gem: rewards.gemsEarned },
        { where: { id: walletId }, transaction }
      );
    }

    // 更新农场区的最后生产时间
    const farmPlots = await FarmPlot.findAll({
      where: { walletId, isUnlocked: true },
      transaction
    });

    for (const plot of farmPlots) {
      plot.lastProductionTime = dayjs(plot.lastProductionTime).add(hours, 'hour').toDate();
      await plot.save({ transaction });
    }

    // 更新出货线的最后出货时间
    const deliveryLine = await DeliveryLine.findOne({
      where: { walletId },
      transaction
    });

    if (deliveryLine) {
      deliveryLine.lastDeliveryTime = dayjs(deliveryLine.lastDeliveryTime).add(hours, 'hour').toDate();
      await deliveryLine.save({ transaction });
    }

    // 记录时间跳跃历史
    await TimeWarpHistory.create({
      walletId,
      productId,
      purchaseId,
      hours,
      gemsEarned: rewards.gemsEarned,
      milkProduced: rewards.milkProduced,
      milkProcessed: rewards.milkProcessed,
      farmProductionPerSecond: rewards.farmProductionPerSecond,
      deliveryProcessingPerSecond: rewards.deliveryProcessingPerSecond,
      hasVip: rewards.hasVip,
      hasSpeedBoost: rewards.hasSpeedBoost,
      speedBoostMultiplier: rewards.speedBoostMultiplier,
      usedAt: dayjs().toDate()
    }, { transaction });

    return rewards;
  }

  /**
   * 预览时间跳跃收益（不执行，仅计算）
   * @param walletId 用户钱包ID
   * @param hours 跳跃小时数
   * @returns 预览收益
   */
  static async previewTimeWarpRewards(walletId: number, hours: number): Promise<TimeWarpResult> {
    return await this.calculateTimeWarpRewards(walletId, hours);
  }
}
