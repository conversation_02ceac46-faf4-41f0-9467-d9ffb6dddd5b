// src/services/ticketService.ts

import { UserWallet } from "../models/UserWallet";
import { Op, Sequelize } from "sequelize";
import { recordWalletHistory } from "./walletHistoryService";
import { t } from "../i18n";

import { GAME_CONFIG } from "../config/consts";

/**
 * 并发安全地购买门票 (atomic update)
 * @param walletId UserWallet表的主键
 * @param quantity 要购买的门票数量
 * @returns 更新后的 wallet 记录
 */
export async function purchaseTicket(
  userId: number,
  walletId: number,
  quantity: number
) {
  if (quantity <= 0) {
    throw new Error(t("errors.quantityMustBePositive"));
  }

  // 每张门票单价 (示例：1 coin)
  const ticketPrice = GAME_CONFIG.TICKET_COST;
  const totalCost = quantity * ticketPrice;

  // 核心：使用 update + where usd >= totalCost
  // - usd = usd - totalCost
  // - ticket = ticket + quantity
  // 并一次性返回更新后的行
  const [affectedCount] = await UserWallet.update(
    {
      // 让 Sequelize 直接在数据库层做自增/自减
      usd: Sequelize.literal(`usd - ${totalCost}`),
      ticket: Sequelize.literal(`ticket + ${quantity}`),
    },
    {
      where: {
        id: walletId,
        usd: {
          [Op.gte]: totalCost, // 保证只有当余额足够时才更新
        },
      },
    }
  );
  // 如果 affectedCount=0，说明没有任何记录被更新，要么钱包不存在，要么余额不足
  if (affectedCount === 0) {
    throw new Error(t("errors.insufficientBalance"));
  }

  await recordWalletHistory(userId, walletId, "usd", totalCost, "Ticket Purchase",'out','usd','usd','Ticket Purchase','ticket_purchase');
  await recordWalletHistory(
    userId,
    walletId,
    "ticket",
    totalCost,
    "Buy Ticket",
    "in",
    "ticket",
    "ticket",
    "Ticket Purchase",
    "ticket_purchase"
  );

  // const updatedWallet = updatedRows[0]; // 取第一条
  const updatedWallet = await UserWallet.findByPk(walletId);
  return updatedWallet;
}
