import { FarmPlot } from '../models/FarmPlot';
import { UserWallet } from '../models/UserWallet';
import { Transaction } from 'sequelize';
import { sequelize } from "../config/db";
import deliveryLineService from './deliveryLineService';
import { FarmPlotCalculator, formatToThreeDecimalsNumber, createBigNumber } from '../utils/bigNumberConfig';
import VipEffectsService from './vipEffectsService';
import BoosterEffectsService from './boosterEffectsService';

class FarmPlotService {
  // 初始化用户的牧场区
  public async initializeUserFarmPlots(walletId: number): Promise<void> {
    const existingPlots = await FarmPlot.findAll({ where: { walletId } });
    
    // 如果用户已经有牧场区记录，则不需要初始化
    if (existingPlots.length > 0) {
      return;
    }
    
    // 创建20个牧场区，但只有第一个是解锁的
    const farmPlots = [];
    
    for (let i = 1; i <= 20; i++) {
      const isFirstPlot = i === 1;
      
      // 固定基础费用
      const baseUpgradeCost = 200;
      
      // 使用新的配置数据计算解锁费用
      const unlockCost = FarmPlotCalculator.calculateUnlockCost(i);

      // 使用新的配置数据计算升级费用
      const upgradeCost = isFirstPlot ? FarmPlotCalculator.calculateUpgradeCostByLevel(i, 1) : 0; // 第一个牧场区初始升级费用，其他未解锁为0

      // 使用新的配置数据计算基础产量
      const baseProduction = isFirstPlot ? FarmPlotCalculator.calculateBaseProduction(1, i) : 0;
      
      farmPlots.push({
        walletId,
        plotNumber: i,
        level: 1, // 所有农场区都是等级1，无论是否解锁
        barnCount: isFirstPlot ? FarmPlotCalculator.calculateBarnCount(1) : 0, // 使用新配置计算牛舍数量
        milkProduction: baseProduction, // 使用新配置计算的产量
        productionSpeed: isFirstPlot ? FarmPlotCalculator.calculateProductionSpeed(1) : 5, // 使用新配置计算生产速度
        unlockCost: unlockCost,
        upgradeCost: upgradeCost, // 使用计算出的升级费用
        lastProductionTime: new Date(),
        isUnlocked: isFirstPlot,
        accumulatedMilk: 0
      });
    }
    
    await FarmPlot.bulkCreate(farmPlots);
  }
  
  // 获取用户的所有牧场区
  public async getUserFarmPlots(walletId: number): Promise<any[]> { // 修改返回类型为 any[]


    // 获取用户的VIP效果和加速道具效果
    const vipEffects = await VipEffectsService.getVipEffects(walletId);
    const boosterEffects = await BoosterEffectsService.getBoosterEffects(walletId);
    
    // 计算总的生产速度加成倍率
    // VIP提供30%的牧场区生产速度加成，Speed Boost提供额外的倍率
    const productionSpeedMultiplier = vipEffects.productionSpeedMultiplier * boosterEffects.speedMultiplier;
    
    const farmPlots = await FarmPlot.findAll({
      where: { walletId },
      order: [['plotNumber', 'ASC']]
    });

    const farmPlotsWithDetails = [];

    // 更新累积的牛奶量并添加增长百分比信息
    for (const plot of farmPlots) {
      if (plot.isUnlocked) {
        // 计算累积牛奶量
        const accumulatedMilk = plot.calculateAccumulatedMilk();
        plot.accumulatedMilk = accumulatedMilk;
        await plot.save(); // 保存累积牛奶量


        // 获取基础生产速度
        const baseProductionSpeed = plot.productionSpeed;

        // 计算下次升级的增长信息（考虑VIP加成）
        const nextUpgradeGrowth = this.calculateNextUpgradeGrowthWithBoosts(plot, productionSpeedMultiplier);

        // 创建包含所有计算属性的普通对象
        const plotDetails = {
          ...plot.get({ plain: true }), // 获取模型的普通对象表示
          // 应用VIP加成效果到生产速度（速度越低越好，所以是除以倍率）
          productionSpeed: formatToThreeDecimalsNumber(baseProductionSpeed / productionSpeedMultiplier),
          // 添加加成信息
          hasBoost: productionSpeedMultiplier > 1,
          boostMultiplier: productionSpeedMultiplier,
          // 添加下次升级增长信息
          nextUpgradeGrowth: nextUpgradeGrowth
        };
        farmPlotsWithDetails.push(plotDetails);
      } else {
        // 对于未解锁的牧场，也将其转换为普通对象并添加到结果中
        const unlockedPlotDetails = {
          ...plot.get({ plain: true }),
          // 未解锁的农场区没有升级预览
          nextUpgradeGrowth: null
        };
        farmPlotsWithDetails.push(unlockedPlotDetails);
      }
    }
    return farmPlotsWithDetails;
  }
  
  // 升级牧场区
  public async upgradeFarmPlot(walletId: number, plotNumber: number): Promise<any> {
    const transaction = await sequelize.transaction();

    try {
      // 获取用户的VIP效果
      const vipEffects = await VipEffectsService.getVipEffects(walletId);

      // 计算生产速度加成倍率（VIP提供30%的牧场区生产速度加成）
      const productionSpeedMultiplier = vipEffects.productionSpeedMultiplier;

      // 获取用户钱包和牧场区
      const userWallet = await UserWallet.findOne({ where: { id: walletId }, transaction });
      const farmPlot = await FarmPlot.findOne({
        where: { walletId, plotNumber },
        transaction
      });

      if (!userWallet || !farmPlot) {
        throw new Error('用户钱包或牧场区不存在');
      }

      if (!farmPlot.isUnlocked) {
        throw new Error('牧场区未解锁');
      }

      if (farmPlot.level >= 20) {
        throw new Error('牧场区已达到最高等级');
      }

      // 检查用户是否有足够的GEM
      const currentGem = Number(userWallet.gem! || 0);

      if (currentGem < farmPlot.upgradeCost) {
        throw new Error('GEM不足');
      }

      // 扣除GEM
      const newGemBN = createBigNumber(userWallet.gem! || 0).minus(farmPlot.upgradeCost);

      userWallet.gem! = newGemBN.toFixed(3);
      await userWallet.save({ transaction });

      // 升级牧场区
      farmPlot.upgrade();
      await farmPlot.save({ transaction });

      await transaction.commit();

      // 获取基础生产速度
      const baseProductionSpeed = farmPlot.productionSpeed;

      // 创建包含核心计算属性的普通对象返回
      const plotDetails = {
        ...farmPlot.get({ plain: true }),
        // 应用VIP加成效果到生产速度（速度越低越好，所以是除以倍率）
        productionSpeed: formatToThreeDecimalsNumber(baseProductionSpeed / productionSpeedMultiplier),
        baseProduction: farmPlot.calculateBaseProduction(),
        // 确保 accumulatedMilk 是最新的
        accumulatedMilk: farmPlot.accumulatedMilk,
        // 添加加成信息
        hasBoost: productionSpeedMultiplier > 1,
        boostMultiplier: productionSpeedMultiplier,
        // 添加 nextUpgradeGrowth 数据（考虑VIP加成）
        nextUpgradeGrowth: this.calculateNextUpgradeGrowthWithBoosts(farmPlot.get({ plain: true }), productionSpeedMultiplier)
      };

      return plotDetails;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
  
  // 解锁牧场区
  public async unlockFarmPlot(walletId: number, plotNumber: number): Promise<any> { // 修改返回类型以包含额外数据
    const transaction = await sequelize.transaction();

    try {
      // 获取用户的VIP效果
      const vipEffects = await VipEffectsService.getVipEffects(walletId);

      // 计算生产速度加成倍率（VIP提供30%的牧场区生产速度加成）
      const productionSpeedMultiplier = vipEffects.productionSpeedMultiplier;

      // 获取用户钱包和牧场区
      const userWallet = await UserWallet.findOne({ where: { id: walletId }, transaction });
      const farmPlot = await FarmPlot.findOne({
        where: { walletId, plotNumber },
        transaction
      });

      if (!userWallet || !farmPlot) {
        throw new Error('用户钱包或牧场区不存在');
      }

      if (farmPlot.isUnlocked) {
        throw new Error('牧场区已解锁');
      }

      // 检查用户是否有足够的GEM
      const currentGem = Number(userWallet.gem! || 0);

      if (currentGem < farmPlot.unlockCost) {
        throw new Error('GEM不足');
      }

      // 获取上一个牧场区的信息，用于计算新牧场区的初始属性
      const previousPlot = await FarmPlot.findOne({
        where: { walletId, plotNumber: plotNumber - 1 },
        transaction
      });

      if (!previousPlot || !previousPlot.isUnlocked) {
        throw new Error('请先解锁前一个牧场区');
      }

      // 扣除GEM
      const newGemBN = createBigNumber(userWallet.gem! || 0).minus(farmPlot.unlockCost);

      userWallet.gem! = newGemBN.toFixed(3);
      await userWallet.save({ transaction });

      // 解锁牧场区并设置初始属性
      farmPlot.isUnlocked = true;
      farmPlot.level = 1; // 等级保持1
      farmPlot.barnCount = FarmPlotCalculator.calculateBarnCount(1); // 使用新配置计算牛舍数量

      // 使用新配置计算基础产量
      farmPlot.milkProduction = FarmPlotCalculator.calculateBaseProduction(1, plotNumber);
      farmPlot.productionSpeed = FarmPlotCalculator.calculateProductionSpeed(1); // 使用新配置计算生产速度

      // 使用新配置计算升级费用
      farmPlot.upgradeCost = FarmPlotCalculator.calculateUpgradeCostByLevel(plotNumber, 1);

      farmPlot.lastProductionTime = new Date();

      // 更新下一个牧场区的解锁费用（如果存在）
      if (plotNumber < 20) {
        const nextPlot = await FarmPlot.findOne({
          where: { walletId, plotNumber: plotNumber + 1 },
          transaction
        });

        if (nextPlot) {
          // 解锁费用从2000开始，每次解锁提升2.0倍
          let unlockCost = 2000 * Math.pow(2.0, nextPlot.plotNumber - 2);

          // 添加上限检查，防止超出INTEGER.UNSIGNED的最大值 (4,294,967,295)
          const MAX_UNLOCK_COST = 4294967295;
          nextPlot.unlockCost = Math.min(unlockCost, MAX_UNLOCK_COST);
          await nextPlot.save({ transaction });
        }
      }

      await farmPlot.save({ transaction });

      await transaction.commit();

      // 获取基础生产速度
      const baseProductionSpeed = farmPlot.productionSpeed;

      // 创建包含核心计算属性的普通对象返回
      const plotDetails = {
        ...farmPlot.get({ plain: true }),
        // 应用VIP加成效果到生产速度（速度越低越好，所以是除以倍率）
        productionSpeed: formatToThreeDecimalsNumber(baseProductionSpeed / productionSpeedMultiplier),
        baseProduction: farmPlot.calculateBaseProduction(),
        // 确保 accumulatedMilk 是最新的, 解锁时通常为0或初始值
        accumulatedMilk: farmPlot.accumulatedMilk,
        // 添加加成信息
        hasBoost: productionSpeedMultiplier > 1,
        boostMultiplier: productionSpeedMultiplier,
        // 添加 nextUpgradeGrowth 数据，确保与 getUserFarmPlots 响应格式一致
        nextUpgradeGrowth: this.calculateNextUpgradeGrowthWithBoosts(farmPlot.get({ plain: true }), productionSpeedMultiplier)
      };

      return plotDetails;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
  
  // 收集牧场区的牛奶
  public async collectMilk(walletId: number, plotNumber: number): Promise<number> {
    const transaction = await sequelize.transaction();
    
    try {
      const farmPlot = await FarmPlot.findOne({
        where: { walletId, plotNumber },
        transaction
      });
      
      if (!farmPlot) {
        throw new Error('牧场区不存在');
      }
      
      if (!farmPlot.isUnlocked) {
        throw new Error('牧场区未解锁');
      }
      
      const collectedMilk = farmPlot.collectMilk();
      await farmPlot.save({ transaction });
      
      // 更新用户钱包中的牛奶数量
      const userWallet = await UserWallet.findOne({ where: { id: walletId }, transaction });
      if (!userWallet) {
        throw new Error('用户钱包不存在');
      }
      
      userWallet.milk += collectedMilk;
      await userWallet.save({ transaction });
      
      await transaction.commit();
      return collectedMilk;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
  
  // 收集所有牧场区的牛奶
  public async collectAllMilk(walletId: number): Promise<number> {
    const transaction = await sequelize.transaction();
    
    try {
      const farmPlots = await FarmPlot.findAll({
        where: { walletId, isUnlocked: true },
        transaction
      });
      
      let totalCollectedMilk = 0;
      
      for (const plot of farmPlots) {
        const collectedMilk = plot.collectMilk();
        totalCollectedMilk += collectedMilk;
        await plot.save({ transaction });
      }
      
      // 更新用户钱包中的牛奶数量
      const userWallet = await UserWallet.findOne({ where: { id: walletId }, transaction });
      if (!userWallet) {
        throw new Error('用户钱包不存在');
      }
      
      userWallet.milk += totalCollectedMilk;
      await userWallet.save({ transaction });
      
      await transaction.commit();
      return totalCollectedMilk;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
  
  // 计算离线收益
  public async calculateOfflineEarnings(walletId: number, offlineTime: number): Promise<number> {
    const transaction = await sequelize.transaction();
    
    try {
      const farmPlots = await FarmPlot.findAll({
        where: { walletId, isUnlocked: true },
        transaction
      });
      
      let totalMilk = 0;
      
      for (const plot of farmPlots) {
        // 计算离线期间完成的生产周期数
        const cycles = Math.floor(offlineTime / plot.productionSpeed);
        
        // 使用新的产量计算公式
        const baseProduction = plot.calculateBaseProduction();
        const milkProduced = cycles * baseProduction * plot.barnCount;
        
        totalMilk += milkProduced;
        
        // 更新上次产出时间和累积牛奶量
        plot.lastProductionTime = new Date(plot.lastProductionTime.getTime() + cycles * plot.productionSpeed * 1000);
        plot.accumulatedMilk += milkProduced;
        
        await plot.save({ transaction });
      }
      
      // 更新用户钱包中的牛奶数量
      const userWallet = await UserWallet.findOne({ where: { id: walletId }, transaction });
      if (!userWallet) {
        throw new Error('用户钱包不存在');
      }
      
      userWallet.milk += totalMilk;
      await userWallet.save({ transaction });
      
      await transaction.commit();
      return totalMilk;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
  
  // 添加牛奶到生产线
  public async addMilkToProductionLine(walletId: number, requestedAmount: number): Promise<{ actualAmount: number, totalProduction: number }> {
    const transaction = await sequelize.transaction();
    
    try {
      // 获取用户的所有已解锁牧场区
      const farmPlots = await FarmPlot.findAll({
        where: { walletId, isUnlocked: true },
        transaction
      });
      
      if (farmPlots.length === 0) {
        throw new Error('没有可用的牧场区');
      }
      
      // 计算牧场区的总产量（每秒）
      let totalProductionPerSecond = 0;
      for (const plot of farmPlots) {
        totalProductionPerSecond += plot.calculateTotalProductionPerSecond();
      }
      
      // 获取用户钱包
      const userWallet = await UserWallet.findOne({ where: { id: walletId }, transaction });
      if (!userWallet) {
        throw new Error('用户钱包不存在');
      }
      
      // 检查用户是否有足够的牛奶
      if (userWallet.milk < requestedAmount) {
        throw new Error('牛奶不足');
      }
      
      // 根据牧场区的实际产量情况，确定实际可以添加的牛奶数量
      // 这里可以根据业务需求调整逻辑，例如限制最大添加量为总产量的10倍
      const maxAllowedAmount = totalProductionPerSecond * 10; // 假设最大允许添加量为每秒产量的10倍
      const actualAmount = Math.min(requestedAmount, maxAllowedAmount);
      
      // 从用户钱包中扣除牛奶
      userWallet.milk -= actualAmount;
      await userWallet.save({ transaction });
      
      // 这里可以添加将牛奶添加到生产线的逻辑
      // 例如更新生产线表中的数据
      // ...
      
      await transaction.commit();
      return { actualAmount, totalProduction: totalProductionPerSecond };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
  
  // 触发牛舍产生牛奶
  public async triggerMilkProduction(walletId: number, plotNumber: number): Promise<{ producedMilk: number, addedToProductionLine: boolean }> {
    const transaction = await sequelize.transaction();
    
    try {
      // 获取指定的牧场区
      const farmPlot = await FarmPlot.findOne({
        where: { walletId, plotNumber, isUnlocked: true },
        transaction
      });
      
      if (!farmPlot) {
        throw new Error('牧场区不存在或未解锁');
      }
      
      // 计算产生的牛奶量
      const baseProduction = farmPlot.calculateBaseProduction();
      const producedMilk = baseProduction * farmPlot.barnCount;
      
      // 更新上次产出时间
      farmPlot.lastProductionTime = new Date();
      await farmPlot.save({ transaction });
      
      // 获取用户钱包
      const userWallet = await UserWallet.findOne({ where: { id: walletId }, transaction });
      if (!userWallet) {
        throw new Error('用户钱包不存在');
      }
      
      // 更新用户钱包中的牛奶数量
      userWallet.milk += producedMilk;
      await userWallet.save({ transaction });
      
      // 获取用户的出货线
      let deliveryLine = await deliveryLineService.initializeUserDeliveryLine(walletId);
      
      // 将产生的牛奶添加到出货线，自动转换为牛奶方块
      deliveryLine.addMilk(producedMilk);
      await deliveryLine.save({ transaction });
      
      // 标记已添加到生产线
      const addedToProductionLine = true;
      
      await transaction.commit();
      return { producedMilk, addedToProductionLine };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  // 计算下次升级的增长信息
  private calculateNextUpgradeGrowth(plot: any): {
    nextProductionSpeed: number;
    nextBarnCount: number;
    nextMilkProduction: number;
  } | null {
    // 如果已达到最高等级，返回null
    if (plot.level >= 20) {
      return null;
    }

    // 使用新配置数据计算升级后的属性
    const nextLevel = plot.level + 1;
    const nextProductionSpeed = formatToThreeDecimalsNumber(
      FarmPlotCalculator.calculateProductionSpeed(nextLevel)
    );

    // 下次升级后的牛舍数量
    const nextBarnCount = FarmPlotCalculator.calculateBarnCount(nextLevel);

    // 下次升级后的产量
    const nextMilkProduction = formatToThreeDecimalsNumber(
      FarmPlotCalculator.calculateBaseProduction(nextLevel, plot.plotNumber)
    );

    return {
      nextProductionSpeed,
      nextBarnCount,
      nextMilkProduction,
    };
  }

  // 计算下次升级的增长信息（考虑VIP加成效果）
  private calculateNextUpgradeGrowthWithBoosts(
    plot: any,
    productionSpeedMultiplier: number
  ): {
    nextProductionSpeed: number;
    nextBarnCount: number;
    nextMilkProduction: number;
  } | null {
    // 如果已达到最高等级，返回null
    if (plot.level >= 20) {
      return null;
    }

    // 计算升级后的基础值
    const nextLevel = plot.level + 1;
    const baseNextProductionSpeed = FarmPlotCalculator.calculateProductionSpeed(nextLevel);
    const baseNextMilkProduction = FarmPlotCalculator.calculateBaseProduction(nextLevel, plot.plotNumber);

    // 应用VIP加成效果
    const nextProductionSpeed = formatToThreeDecimalsNumber(
      baseNextProductionSpeed / productionSpeedMultiplier
    );

    // 下次升级后的牛舍数量
    const nextBarnCount = FarmPlotCalculator.calculateBarnCount(nextLevel);

    // 下次升级后的产量 (不受VIP加成影响)
    const nextMilkProduction = formatToThreeDecimalsNumber(baseNextMilkProduction);

    return {
      nextProductionSpeed,
      nextBarnCount,
      nextMilkProduction,
    };
  }
}

export default new FarmPlotService();