# 核心游戏循环（Core Game Loop）实现文档

## 系统概述

核心游戏循环是Moofun奶牛农场游戏的中枢系统，负责整合牧场区系统和出货线系统，实现资源流转逻辑（牛奶→宝石→升级），确保游戏平衡性，并实现离线收益计算。系统设计遵循「养牛产奶→出售牛奶获得宝石→升级设施→提升产能」的循环模式，为玩家提供持续的游戏乐趣。

## 核心功能

### 1. 系统整合

核心游戏循环将牧场区系统和出货线系统整合在一起，实现了以下功能：

- **资源流转逻辑**：牧场区产出牛奶 → 出货线将牛奶打包成方块 → 出售方块获得宝石 → 使用宝石升级设施
- **数据一致性**：通过事务管理确保各系统间数据一致性，防止资源丢失或重复计算
- **实时更新**：通过`processGameLoop`方法实现游戏状态的实时更新，确保资源产出和消耗的及时计算

### 2. 游戏状态管理

- **游戏状态获取**：通过`getUserGameState`方法获取用户的完整游戏状态，包括牧场区、出货线和资源信息
- **离线收益计算**：通过`calculateOfflineEarnings`方法计算用户离线期间的收益，包括牛奶产出和宝石获取
- **游戏统计数据**：提供总产能、出货效率等统计数据，帮助玩家了解自己的游戏进度

### 3. 离线收益

离线收益系统确保玩家在不在线时也能获得游戏进展：

- **离线时间计算**：准确计算玩家离线的时间长度
- **资源累积**：根据离线时间和玩家设施等级计算累积的牛奶和宝石
- **收益展示**：玩家返回游戏时展示离线期间获得的收益汇总

### 4. 排行榜系统

- **宝石排名**：根据玩家拥有的宝石数量进行排名
- **排行榜获取**：通过`getLeaderboard`方法获取排行榜数据

### 5. 数据分析

- **用户行为数据收集**：通过`collectUserBehaviorData`方法收集玩家行为数据
- **关键指标监控**：为游戏平衡调整提供数据支持

## 数据模型关系

核心游戏循环涉及以下数据模型：

- **UserWallet**：存储用户的牛奶和宝石资源
- **FarmPlot**：牧场区模型，负责牛奶生产
- **DeliveryLine**：出货线模型，负责将牛奶转换为宝石

## API接口

### 1. 获取游戏状态

```
GET /api/game-loop/state
```

返回用户的完整游戏状态，包括牧场区、出货线和资源信息。

### 2. 处理游戏循环

```
POST /api/game-loop/process
```

执行一次游戏循环迭代，更新牛奶产出和宝石获取。

### 3. 计算离线收益

```
POST /api/game-loop/offline-earnings
```

计算用户离线期间的收益，包括牛奶产出和宝石获取。

### 4. 获取排行榜

```
GET /api/game-loop/leaderboard
```

获取宝石排行榜数据。

### 5. 收集用户行为数据

```
POST /api/game-loop/collect-data
```

收集用户行为数据，用于游戏分析和平衡调整。

## 游戏平衡

核心游戏循环的设计考虑了游戏平衡性：

- **初始阶段**：玩家从单个牧场区开始，快速获得成就感
- **中期阶段**：提供多样化的升级选择（升级牧场区或出货线）
- **后期阶段**：解锁新牧场区，保持足够的挑战性和成长空间

## 经济系统

- **牛奶**：作为中间资源，不可直接消费，需要通过出货线转换为宝石
- **宝石（GEM）**：作为主要货币，用于所有升级和解锁
- **平衡机制**：牧场区和出货线的升级成本和收益曲线经过精心设计，确保宝石收入与支出的平衡

## 实现细节

### 牛奶产出计算

```typescript
// 计算离线期间产出的牛奶总量
let totalMilkProduced = 0;
for (const plot of farmPlots) {
  // 计算该牧场区离线期间的产出次数
  const productionTimes = Math.floor(offlineSeconds / plot.productionSpeed);
  // 计算该牧场区离线期间产出的牛奶量
  const milkProduced = productionTimes * plot.milkProduction;
  totalMilkProduced += milkProduced;
}
```

### 宝石收益计算

```typescript
// 计算离线期间可以出售的牛奶方块数量
const deliveryTimes = Math.floor(offlineSeconds / deliveryLine.deliverySpeed);
// 计算可以处理的牛奶总量
const processableMilk = Math.min(totalMilkProduced, deliveryTimes * deliveryLine.blockUnit);
// 计算可以创建的牛奶方块数量
const blocksCreated = Math.floor(processableMilk / deliveryLine.blockUnit);
// 计算获得的宝石数量
const gemsEarned = blocksCreated * deliveryLine.blockPrice;
```

## 总结

核心游戏循环系统成功整合了牧场区系统和出货线系统，实现了资源流转逻辑，确保了游戏平衡性，并实现了离线收益计算。系统设计遵循「养牛产奶→出售牛奶获得宝石→升级设施→提升产能」的循环模式，为玩家提供持续的游戏乐趣。