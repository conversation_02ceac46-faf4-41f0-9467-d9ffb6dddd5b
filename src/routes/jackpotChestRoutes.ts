import express from "express";
import * as jackpotChestController from "../controllers/jackpotChestController";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { languageMiddleware } from "../middlewares/languageMiddleware";
const router = express.Router();

// 所有路由都需要身份验证
router.use(walletAuthMiddleware);
router.use(languageMiddleware);

// 获取宝箱倒计时状态
router.get("/countdown", walletAuthMiddleware, async (req, res) => {
  await jackpotChestController.getChestCountdownStatus(req, res);
});

// 加速宝箱倒计时
router.post("/accelerate", walletAuthMiddleware, async (req, res) => {
  await jackpotChestController.accelerateChestCountdown(req, res);
});

// 获取用户的宝箱加速状态
router.get("/acceleration-status", walletAuthMiddleware, async (req, res) => {
  await jackpotChestController.getAccelerationStatus(req, res);
});

// 领取倒计时宝箱
router.post("/collect", walletAuthMiddleware, async (req, res) => {
  await jackpotChestController.collectJackpotChest(req, res);
});

// 使用分享助力链接
router.post("/boost", walletAuthMiddleware, async (req, res) => {
  await jackpotChestController.useShareBoostLink(req, res);
});

// 切换自动领取状态
router.post("/auto-collect", walletAuthMiddleware, async (req, res) => {
  await jackpotChestController.toggleAutoCollect(req, res);
});

// 获取Jackpot奖池状态
router.get("/pool-status", walletAuthMiddleware, async (req, res) => {
  await jackpotChestController.getJackpotPoolStatus(req, res);
});

// 新增：获取用户发出的助力历史（分页）
// router.get("/outgoing-boost-history", walletAuthMiddleware, async (req, res) => {
//   await jackpotChestController.getUserOutgoingBoostHistory(req, res);
// });

// 新增：获取用户收到的助力历史（分页）
router.get("/incoming-boost-history", walletAuthMiddleware, async (req, res) => {
  await jackpotChestController.getUserIncomingBoostHistory(req, res);
});

// 获取用户的分享助力链接
router.get("/share-links", walletAuthMiddleware, async (req, res) => {
  await jackpotChestController.getUserShareBoostLinks(req, res);
});

// 获取用户的分享助力历史
router.get("/share-boost-history", walletAuthMiddleware, async (req, res) => {
  await jackpotChestController.getShareBoostHistory(req, res);
});

// 获取用户的加速信息
router.get("/boost-info", walletAuthMiddleware, async (req, res) => {
  await jackpotChestController.getUserBoostInfo(req, res);
});

// 获取一次性4个宝箱
router.post("/collect-four-chests", walletAuthMiddleware, async (req, res) => {
  await jackpotChestController.collectFourChests(req, res);
});

// 获取四个宝箱领取状态
router.get("/four-chests-status", walletAuthMiddleware, async (req, res) => {
  await jackpotChestController.getFourChestsCollectStatus(req, res);
});

export default router;