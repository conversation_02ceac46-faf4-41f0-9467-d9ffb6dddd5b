// src/routes/ticketRoutes.ts
import { Router } from "express";
import { purchaseTicket } from "../services/ticketService";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { MyRequest } from "../types/customRequest";
import { ajv, tFromRequest, formatValidationErrors } from "../i18n";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { successResponse, errorResponse } from "../utils/responseUtil";

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

// 定义购买门票请求体验证模式
const purchaseTicketSchema = {
  type: "object",
  properties: {
    quantity: { type: "integer", minimum: 1 }
  },
  required: ["quantity"]
};

const validatePurchaseTicket = ajv.compile(purchaseTicketSchema);

/**
 * POST /api/ticket/purchase
 * body: { quantity }
 * header: { Authorization: "Bearer <token>" }
 */
//@ts-ignore
router.post("/purchase", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;

    // 验证请求体
    const valid = validatePurchaseTicket(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validatePurchaseTicket.errors || [], req.language)
      ));
    }

    const { userId, walletId } = myReq.user!;
    const { quantity } = req.body;

    const wallet = await purchaseTicket(userId!, walletId!, quantity);
    res.json(successResponse(
      {
        balance: {
          usd: wallet?.usd,
          ticket: wallet?.ticket,
        }
      },
      tFromRequest(req, "success.ticketPurchase")
    ));
  } catch (err: any) {
    res.status(400).json(errorResponse(err.message));
  }
});

export default router;
