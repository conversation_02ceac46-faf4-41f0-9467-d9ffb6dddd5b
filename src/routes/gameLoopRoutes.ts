import express from 'express';
import gameLoopController from '../controllers/gameLoopController';
import { walletAuthMiddleware } from '../middlewares/walletAuth';

const router = express.Router();

// 获取用户的游戏状态
router.get('/state', walletAuthMiddleware, gameLoopController.getUserGameState);

// 处理游戏循环的一次迭代（实时更新）
router.post('/process', walletAuthMiddleware, gameLoopController.processGameLoop);

// 计算离线收益
router.post('/offline-earnings', walletAuthMiddleware, gameLoopController.calculateOfflineEarnings);

// 获取排行榜
router.get('/leaderboard', gameLoopController.getLeaderboard);

// 收集用户行为数据
router.post('/collect-data', walletAuthMiddleware, gameLoopController.collectUserBehaviorData);

export default router;