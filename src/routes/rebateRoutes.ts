// src/routes/rebateRoutes.ts
import { Router } from "express";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { MyRequest } from "../types/customRequest";
import { 
  getDailyRebateDetails, 
  getPendingRebateAmount, 
  claimPendingRebates, 
  isEligibleForRebate, 
  getDailyPromotionRebateHistory
} from "../services/ticketRebateService";
import { ajv, tFromRequest, formatValidationErrors } from "../i18n";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { successResponse, errorResponse } from "../utils/responseUtil";

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

/**
 * @api {get} /api/rebate/daily-details 获取每日推荐返利详情
 * @apiName GetDailyRebateDetails
 * @apiGroup Rebate
 * @apiParam {Number} [days=7] 查询天数
 * @apiSuccess {Object} data 返利详情数据
 */

// 定义每日返利详情查询参数验证模式
const dailyRebateDetailsSchema = {
  type: "object",
  properties: {
    page: { type: "string", pattern: "^[0-9]+$" },
    limit: { type: "string", pattern: "^[0-9]+$" }
  },
  additionalProperties: false
};

const validateDailyRebateDetails = ajv.compile(dailyRebateDetailsSchema);

//@ts-ignore
router.get("/daily-details", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const { walletId } = myReq.user!;
    
    // 验证请求参数
    const valid = validateDailyRebateDetails(req.query);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateDailyRebateDetails.errors || [], req.language)
      ));
    }
    
    if (!walletId) {
      return res.status(400).json(errorResponse(tFromRequest(req, "errors.missingWalletId")));
    }
    
    const page = req.query.page ? parseInt(req.query.page as string) : 1;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
    
    const details = await getDailyPromotionRebateHistory(walletId, page, limit);
    
    res.json(successResponse(details, tFromRequest(req, "success.getDailyRebateDetails")));
  } catch (error) {
    console.error("获取每日推荐返利详情失败:", error);
    res.status(500).json(errorResponse(
      tFromRequest(req, "errors.getDailyRebateDetailsFailed"),
      (error as Error).message
    ));
  }
});

/**
 * @api {get} /api/rebate/pending-amount 获取待领取返利金额
 * @apiName GetPendingRebateAmount
 * @apiGroup Rebate
 * @apiSuccess {Number} data.amount 待领取金额
 * @apiSuccess {Boolean} data.canClaim 是否可以领取
 */

// 定义待领取返利金额查询参数验证模式
const pendingRebateAmountSchema = {
  type: "object",
  properties: {},
  additionalProperties: false
};

const validatePendingRebateAmount = ajv.compile(pendingRebateAmountSchema);

//@ts-ignore
router.get("/pending-amount", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const { walletId } = myReq.user!;
    
    // 验证请求参数
    const valid = validatePendingRebateAmount(req.query);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validatePendingRebateAmount.errors || [], req.language)
      ));
    }
    
    if (!walletId) {
      return res.status(400).json(errorResponse(tFromRequest(req, "errors.missingWalletId")));
    }
    
    const amount = await getPendingRebateAmount(walletId);
    const canClaim = await isEligibleForRebate(walletId);
    
    res.json(successResponse({
      amount,
      canClaim
    }, tFromRequest(req, "success.getPendingRebateAmount")));
  } catch (error) {
    console.error("获取待领取返利金额失败:", error);
    res.status(500).json(errorResponse(
      tFromRequest(req, "errors.getPendingRebateAmountFailed"),
      (error as Error).message
    ));
  }
});

/**
 * @api {post} /api/rebate/claim 领取待领取返利
 * @apiName ClaimPendingRebates
 * @apiGroup Rebate
 * @apiSuccess {Number} data.amount 领取金额
 */

// 定义领取返利请求体验证模式
const claimRebateSchema = {
  type: "object",
  properties: {},
  additionalProperties: false
};

const validateClaimRebate = ajv.compile(claimRebateSchema);

//@ts-ignore
router.post("/claim", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const { walletId } = myReq.user!;
    
    // 验证请求体
    const valid = validateClaimRebate(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateClaimRebate.errors || [], req.language)
      ));
    }
    
    if (!walletId) {
      return res.status(400).json(errorResponse(tFromRequest(req, "errors.missingWalletId")));
    }
    
    const amount = await claimPendingRebates(walletId);
    
    res.json(successResponse({ amount }, tFromRequest(req, "success.claimRebateSuccess")));
  } catch (error) {
    console.error("领取返利失败:", error);
    res.status(400).json(errorResponse(
      (error as Error).message
    ));
  }
});

export default router;