
services:
  redis:
    image: redis:6
    restart: always
    ports:
      - "6257:6379"
    privileged: true
    volumes:
      - ./redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - wolf_fun

  redisinsight:
    image: redislabs/redisinsight:latest
    restart: always
    ports:
      - "5577:5540"
    depends_on:
      - redis
    volumes:
      - ./redis-insight:/db
    networks:
      - wolf_fun

  mysql:
    image: mysql:8.3.0
    container_name: mysql-8.3.0-wolf
    environment:
      MYSQL_ROOT_PASSWORD: 00321zixun
      MYSQL_DATABASE: wolf
      MYSQL_USER: wolf
      MYSQL_PASSWORD: 00321zixunadmin
      TZ: Asia/Shanghai
    volumes:
      - ./mysql-data:/var/lib/mysql
      - ./mysqld.cnf:/etc/mysql/conf.d/mysqld.cnf
    ports:
      - "3669:3306"
    networks:
      - wolf_fun
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u$$MYSQL_USER", "-p$$MYSQL_PASSWORD"]
      interval: 10s
      timeout: 5s
      retries: 5
  
  phpmyadmin:
    image: phpmyadmin:5.2.1
    container_name: phpmyadmin5.2.1-wolf
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      PMA_ARBITRARY: 1
      PMA_HOST: mysql
    ports:
      - "8269:80"
    volumes:
      - ./uploads.ini:/usr/local/etc/php/conf.d/uploads.ini:ro
    networks:
      - wolf_fun

networks:
  wolf_fun:
    name: wolf_fun
    driver: bridge