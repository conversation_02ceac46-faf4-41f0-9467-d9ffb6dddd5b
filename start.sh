#!/bin/bash

# Wolf Fun 游戏服务器启动脚本
# 确保使用 Node.js 22 版本

echo "🚀 启动 Wolf Fun 游戏服务器..."

# 检查是否安装了 nvm
if [ -s "$HOME/.nvm/nvm.sh" ]; then
    echo "📦 加载 NVM..."
    source "$HOME/.nvm/nvm.sh"
    
    # 使用 .nvmrc 文件中指定的 Node.js 版本
    if [ -f ".nvmrc" ]; then
        echo "🔧 使用 .nvmrc 中指定的 Node.js 版本..."
        nvm use
    else
        echo "🔧 使用 Node.js 22..."
        nvm use 22
    fi
else
    echo "⚠️  NVM 未安装，使用系统默认 Node.js 版本"
fi

# 显示当前 Node.js 版本
echo "📋 当前 Node.js 版本: $(node --version)"
echo "📋 当前 NPM 版本: $(npm --version)"

# 检查 Node.js 版本是否符合要求
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 22 ]; then
    echo "❌ 错误: 需要 Node.js 22 或更高版本，当前版本: $(node --version)"
    echo "请安装 Node.js 22:"
    echo "  nvm install 22"
    echo "  nvm use 22"
    exit 1
fi

# 检查环境变量
if [ -z "$NODE_ENV" ]; then
    echo "🔧 设置环境变量 NODE_ENV=development"
    export NODE_ENV=development
fi

echo "🌍 运行环境: $NODE_ENV"

# 检查依赖是否已安装
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
fi

# 启动开发服务器
echo "🎮 启动 Wolf Fun 游戏服务器..."
echo "📍 服务器地址: http://localhost:3456"
echo "🔧 测试重置 API: http://localhost:3456/api/test/health"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "========================"

npm run dev
