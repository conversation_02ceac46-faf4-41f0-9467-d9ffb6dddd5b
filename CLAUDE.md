# CLAUDE.md

本文件为Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 项目概述

Wolf Fun 是一个基于TypeScript的游戏应用，集成了Kaia链的DappPortal平台。这是一个农场模拟游戏，玩家管理奶牛农场，生产牛奶，并通过出货线将牛奶转换为宝石。

## 开发命令

### 核心命令
- `npm run dev` - 启动开发服务器，支持自动重载
- `npm run build` - 生产环境构建
- `npm run start` - 启动生产服务器
- `npm run seed:tasks` - 运行数据库迁移和种子数据

### 数据库命令
- `npx sequelize-cli db:migrate` - 运行数据库迁移
- `npx sequelize-cli db:seed --seed <filename>` - 运行特定种子文件

## 架构概览

### 核心应用结构
- **主服务器**: `src/app.ts` - Express.js服务器，包含完整的中间件、路由注册和优雅关闭处理
- **数据库层**: 使用MySQL的Sequelize ORM
- **队列系统**: 使用Redis的BullMQ后台任务处理
- **DappPortal集成**: Kaia链的DappPortal平台集成，支持Mini DApp和支付功能

### 关键系统
1. **牧场区系统**: 核心牛奶生产设施，具有升级机制
2. **出货线系统**: 通过自动化处理将牛奶转换为宝石
3. **核心游戏循环**: 整合牧场区和出货线实现资源流转
4. **IAP系统**: 应用内购买和加速器管理
5. **奖池系统**: 自动宝箱收集和奖励
6. **DappPortal支付系统**: 集成Kaia链支付和Stripe法币支付

### 目录结构
- `src/models/` - Sequelize数据模型，包含完整的关联关系
- `src/controllers/` - HTTP请求处理器
- `src/services/` - 业务逻辑层
- `src/routes/` - API路由定义
- `src/jobs/` - 后台任务处理器（BullMQ工作器）
- `src/middleware/` - Express中间件（认证、语言等）
- `src/utils/` - 工具函数和帮助器
- `src/config/` - 应用配置
- `migrations/` - 数据库架构迁移
- `seeders/` - 数据库种子数据

## 数据库配置

使用Sequelize配合MySQL。配置文件在`config/config.js`，支持开发、测试和生产环境的环境变量覆盖。

关键模型包括：
- `UserWallet` - 主要用户实体，包含游戏资源
- `FarmPlot` - 牛奶生产设施
- `DeliveryLine` - 牛奶转宝石系统
- `IapProduct/IapPurchase` - 应用内购买系统
- `JackpotPool` - 奖池奖励系统

## Docker设置

开发环境使用Docker Compose，包含：
- MySQL 8.3.0 (端口3669)
- Redis 6 (端口6257)
- phpMyAdmin (端口8269)
- RedisInsight (端口5577)

## 关键特性

### 游戏机制
- **资源流转**: 牛奶 → 宝石 → 升级 → 提升产能
- **离线收益**: 计算离线期间的生产收益
- **VIP系统**: 高级会员制，享受增强福利
- **加速器系统**: 临时生产增强效果

### 技术特性
- **优雅关闭**: 全面清理HTTP服务器、队列和连接
- **任务调度**: 基于Cron的任务调度，用于奖励和结算
- **国际化**: 多语言支持（EN、JA、ZH）
- **速率限制**: API速率限制保障安全
- **错误处理**: 集中式错误处理和日志记录

## 开发指南

### 代码组织
- 遵循分层架构：Routes → Controllers → Services → Models
- 使用TypeScript确保类型安全
- 实现适当的错误处理和try-catch块
- 使用数据库事务确保数据一致性

### 添加新功能
1. 在`src/models/`中创建数据模型
2. 在`migrations/`中添加数据库迁移
3. 在`src/services/`中实现业务逻辑
4. 在`src/controllers/`中创建控制器
5. 在`src/routes/`中定义路由
6. 在`src/app.ts`中注册路由

### 测试和验证
- 使用适当的TypeScript类型和接口
- 通过中间件实现输入验证
- 对复杂操作使用数据库事务
- 使用适当的认证测试API端点

## 环境变量

必需的关键环境变量：
- 数据库：`DB_HOST`、`DB_USER`、`DB_PASS`、`DB_NAME`
- Redis：Redis连接设置
- DappPortal：Kaia链相关配置和API密钥

## 性能考虑

- 使用BigNumber.js进行精确的小数计算
- 为数据库实现连接池
- 使用Redis进行缓存和任务队列
- 通过适当的索引优化数据库查询
- 为API保护实现速率限制

## 安全注意事项

- 使用JWT进行身份验证
- 通过Sequelize净化数据库输入
- 实现适当的错误处理以防止信息泄露
- 使用环境变量管理敏感配置

## DappPortal集成

### 关于DappPortal
DappPortal是Kaia链的官方DApp平台，为用户提供Web3服务的无缝体验。用户可以直接访问和使用各种Mini DApp，无需下载额外应用。

### Kaia链特性
- **EVM兼容**: 支持以太坊开发者轻松迁移DApp
- **高性能**: 1秒区块时间，理论吞吐量4000 TPS
- **用户友好**: 支持账户抽象、gas费代付等功能
- **社交集成**: 支持社交登录和分享功能

### 支付系统
- **Kaia支付**: 支持KAIA代币的加密货币支付
- **法币支付**: 集成Stripe的法币支付系统
- **钱包集成**: 支持社交登录创建钱包
- **OKX钱包**: 支持OKX钱包集成

### Mini DApp生态
Wolf Fun作为Mini DApp运行在DappPortal平台上，享受以下优势：
- 无需用户下载安装，直接在浏览器中运行
- 无缝的Web3用户体验
- 内置钱包和支付功能
- 社交分享和推广功能

## 游戏系统详解

### 牧场区系统
- 20个牧场区，初始只有第1个解锁
- 升级机制：提升等级、增加牛舍、提升产量和速度
- 解锁机制：使用宝石解锁新牧场区

### 出货线系统
- 将牛奶自动打包成牛奶方块
- 自动出售方块获得宝石收入
- 升级提升处理速度和方块价值

### 核心游戏循环
- 整合牧场区和出货线系统
- 实现资源流转逻辑
- 计算离线收益
- 提供排行榜和统计数据