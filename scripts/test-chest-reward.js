'use strict';

// 此脚本用于测试宝箱奖励功能
const axios = require('axios');
const dotenv = require('dotenv');
dotenv.config();

// 测试配置
const TEST_WALLET_ID = 1; // 替换为实际的测试钱包ID
const TEST_USER_ID = 1;   // 替换为实际的测试用户ID

// 模拟generateChestReward函数
function generateChestReward() {
  // 生成随机数在指定范围内
  const getRandomInt = (min, max) => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  };

  // 根据概率决定是否添加物品
  const shouldAddItem = (probability) => {
    return Math.random() * 100 < probability;
  };
  
  const random = Math.random() * 100; // 转换为百分比
  const items = [];
  let level = 1;
  
  // LV1宝箱 - 60% 概率
  if (random < 60) {
    level = 1;
    
    // 绿色碎片16-24 (100%概率)
    items.push({
      type: 'fragment_green',
      amount: getRandomInt(16, 24)
    });
    
    // 蓝色碎片1-4 (30%概率)
    if (shouldAddItem(30)) {
      items.push({
        type: 'fragment_blue',
        amount: getRandomInt(1, 4)
      });
    }
    
    // 宝石1,000-5,000 (100%概率)
    items.push({
      type: 'gem',
      amount: getRandomInt(1000, 5000)
    });
  }
  // LV2宝箱 - 28% 概率
  else if (random < 88) { // 60 + 28 = 88
    level = 2;
    
    // 绿色碎片8-15 (100%概率)
    items.push({
      type: 'fragment_green',
      amount: getRandomInt(8, 15)
    });
    
    // 蓝色碎片4-8 (100%概率)
    items.push({
      type: 'fragment_blue',
      amount: getRandomInt(4, 8)
    });
    
    // 紫色碎片0-1 (20%概率)
    if (shouldAddItem(20)) {
      items.push({
        type: 'fragment_purple',
        amount: 1
      });
    }
    
    // 宝石5,000-15,000 (100%概率)
    items.push({
      type: 'gem',
      amount: getRandomInt(5000, 15000)
    });
  }
  // LV3宝箱 - 10% 概率
  else if (random < 98) { // 88 + 10 = 98
    level = 3;
    
    // 蓝色碎片6-12 (100%概率)
    items.push({
      type: 'fragment_blue',
      amount: getRandomInt(6, 12)
    });
    
    // 紫色碎片2-4 (100%概率)
    items.push({
      type: 'fragment_purple',
      amount: getRandomInt(2, 4)
    });
    
    // 金色碎片0-1 (28%概率)
    if (shouldAddItem(28)) {
      items.push({
        type: 'fragment_gold',
        amount: 1
      });
    }
    
    // 宝石20,000-50,000 (100%概率)
    items.push({
      type: 'gem',
      amount: getRandomInt(20000, 50000)
    });
  }
  // LV4宝箱 - 2% 概率
  else {
    level = 4;
    
    // 紫色碎片6-10 (100%概率)
    items.push({
      type: 'fragment_purple',
      amount: getRandomInt(6, 10)
    });
    
    // 金色碎片2-4 (100%概率)
    items.push({
      type: 'fragment_gold',
      amount: getRandomInt(2, 4)
    });
    
    // 宝石50,000-100,000 (100%概率)
    items.push({
      type: 'gem',
      amount: getRandomInt(50000, 100000)
    });
  }

  return {
    level,
    items
  };
}

// 测试奖励生成功能
function testRewardGeneration() {
  console.log('测试奖励生成功能');
  
  // 生成100个奖励进行统计
  const results = {
    level1: 0,
    level2: 0,
    level3: 0,
    level4: 0
  };
  
  for (let i = 0; i < 1000; i++) {
    const reward = generateChestReward();
    results[`level${reward.level}`]++;
  }
  
  console.log('奖励生成统计 (1000次):');
  console.table(results);
  console.log(`1级宝箱概率: ${(results.level1 / 1000 * 100).toFixed(2)}%`);
  console.log(`2级宝箱概率: ${(results.level2 / 1000 * 100).toFixed(2)}%`);
  console.log(`3级宝箱概率: ${(results.level3 / 1000 * 100).toFixed(2)}%`);
  console.log(`4级宝箱概率: ${(results.level4 / 1000 * 100).toFixed(2)}%`);
}

// 直接使用mysql查询检查宝箱奖励是否正确应用到数据库
async function testDatabaseUpdates() {
  const mysql = require('mysql2/promise');
  let connection;
  
  try {
    console.log('\n测试数据库更新');
    
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASS || '',
      database: process.env.DB_NAME || 'wolf'
    });
    
    // 1. 查询测试钱包当前状态
    console.log(`查询钱包ID ${TEST_WALLET_ID} 的当前状态`);
    const [walletBefore] = await connection.query(`
      SELECT id, gem, ticket, ticket_fragment, ton 
      FROM user_wallets 
      WHERE id = ?
    `, [TEST_WALLET_ID]);
    
    console.log('测试钱包当前状态:');
    console.table(walletBefore);
    
    // 2. 创建测试宝箱
    console.log('创建测试宝箱');
    await connection.query(`
      INSERT INTO chests (walletId, userId, isOpened, type, createdAt, updatedAt) 
      VALUES (?, ?, false, 'test', NOW(), NOW())
    `, [TEST_WALLET_ID, TEST_USER_ID]);
    
    const [insertResult] = await connection.query('SELECT LAST_INSERT_ID() as id');
    const chestId = insertResult[0].id;
    console.log(`创建宝箱成功，ID: ${chestId}`);
    
    // 3. 生成模拟奖励 - 假设是2级宝箱
    const testReward = {
      level: 2,
      items: [
        { type: 'fragment_green', amount: 10 },
        { type: 'fragment_blue', amount: 6 },
        { type: 'fragment_purple', amount: 1 },
        { type: 'gem', amount: 10000 }
      ]
    };
    
    console.log('模拟奖励:', testReward);
    
    // 4. 应用奖励到钱包
    console.log('应用奖励到钱包');
    for (const item of testReward.items) {
      await connection.query(`
        UPDATE user_wallets 
        SET ${item.type} = ${item.type} + ? 
        WHERE id = ?
      `, [item.amount, TEST_WALLET_ID]);
      
      // 添加钱包历史记录
      await connection.query(`
        INSERT INTO wallet_history (
          userId, walletId, amount, currency, reference, 
          action, category, credit_type, fe_display_remark, 
          developer_remark, createdAt, updatedAt
        ) 
        VALUES (
          ?, ?, ?, ?, 'Chest Reward', 
          'in', ?, ?, ?, 
          ?, NOW(), NOW()
        )
      `, [
        TEST_USER_ID, 
        TEST_WALLET_ID, 
        item.amount, 
        item.type, 
        item.type, 
        item.type,
        `宝箱奖励 - ${item.type.toUpperCase()} - 等级${testReward.level}`,
        `宝箱奖励 - 宝箱ID: ${chestId} - 等级${testReward.level}`
      ]);
    }
    
    // 5. 更新宝箱状态为已开启
    await connection.query(`
      UPDATE chests 
      SET isOpened = true, updatedAt = NOW() 
      WHERE id = ?
    `, [chestId]);
    
    // 6. 查询测试钱包更新后状态
    console.log('查询钱包更新后状态');
    const [walletAfter] = await connection.query(`
      SELECT id, gem, ticket, ticket_fragment, ton 
      FROM user_wallets 
      WHERE id = ?
    `, [TEST_WALLET_ID]);
    
    console.log('测试钱包更新后状态:');
    console.table(walletAfter);
    
    // 7. 查询钱包历史记录
    console.log('查询最近钱包历史记录');
    const [history] = await connection.query(`
      SELECT id, walletId, currency, amount, reference, fe_display_remark 
      FROM wallet_history 
      WHERE walletId = ? 
      ORDER BY id DESC 
      LIMIT 5
    `, [TEST_WALLET_ID]);
    
    console.log('最近钱包历史记录:');
    console.table(history);
    
    console.log('测试完成!');
    
  } catch (error) {
    console.error('测试过程中出错:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 执行测试
async function runTests() {
  console.log('开始测试宝箱奖励功能');
  console.log('====================');
  
  // 测试奖励生成
  testRewardGeneration();
  
  // 测试数据库更新
  await testDatabaseUpdates();
  
  console.log('====================');
  console.log('测试完成!');
}

runTests().catch(console.error);