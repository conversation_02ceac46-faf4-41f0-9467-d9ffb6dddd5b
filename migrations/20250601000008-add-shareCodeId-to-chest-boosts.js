'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 检查shareCodeId列是否已存在
    const columns = await queryInterface.describeTable('chest_boosts');
    if (!columns.shareCodeId) {
      await queryInterface.addColumn('chest_boosts', 'shareCodeId', {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: true,
        references: {
          model: 'share_boost_links',
          key: 'id'
        },
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE'
      });
    }

    // 添加索引以提高查询性能
    await queryInterface.addIndex('chest_boosts', ['shareCodeId']);
  },

  down: async (queryInterface, Sequelize) => {
    // 检查shareCodeId列是否存在
    const columns = await queryInterface.describeTable('chest_boosts');
    if (columns.shareCodeId) {
      // 先删除索引
      await queryInterface.removeIndex('chest_boosts', ['shareCodeId']);
      
      // 再删除列
      await queryInterface.removeColumn('chest_boosts', 'shareCodeId');
    }
  }
};