module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addConstraint('chest_countdowns', {
      fields: ['walletId'],
      type: 'foreign key',
      name: 'fk_chest_countdowns_walletId',
      references: {
        table: 'user_wallets',
        field: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeConstraint('chest_countdowns', 'fk_chest_countdowns_walletId');
  }
};