'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 检查targetTelegramId列是否已存在
    const columns = await queryInterface.describeTable('chest_boosts');
    if (!columns.targetTelegramId) {
      await queryInterface.addColumn('chest_boosts', 'targetTelegramId', {
        type: Sequelize.STRING,
        allowNull: true
      });
    }

    // 获取表的外键约束
    const foreignKeys = await queryInterface.getForeignKeyReferencesForTable('chest_boosts');
    
    // 找到与targetUserId相关的外键约束
    const targetUserIdForeignKey = foreignKeys.find(fk => fk.columnName === 'targetUserId');
    if (targetUserIdForeignKey) {
      await queryInterface.removeConstraint('chest_boosts', targetUserIdForeignKey.constraintName);
    }
    
    // 找到与targetWalletId相关的外键约束
    const targetWalletIdForeignKey = foreignKeys.find(fk => fk.columnName === 'targetWalletId');
    if (targetWalletIdForeignKey) {
      await queryInterface.removeConstraint('chest_boosts', targetWalletIdForeignKey.constraintName);
    }

    // 修改targetUserId和targetWalletId为可空
    await queryInterface.changeColumn('chest_boosts', 'targetUserId', {
      type: Sequelize.INTEGER.UNSIGNED,
      allowNull: true
    });

    await queryInterface.changeColumn('chest_boosts', 'targetWalletId', {
      type: Sequelize.INTEGER.UNSIGNED,
      allowNull: true
    });
    
    // 重新添加外键约束（如果之前存在）
    if (targetUserIdForeignKey) {
      await queryInterface.addConstraint('chest_boosts', {
        fields: ['targetUserId'],
        type: 'foreign key',
        name: targetUserIdForeignKey.constraintName,
        references: {
          table: targetUserIdForeignKey.referencedTableName,
          field: targetUserIdForeignKey.referencedColumnName
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
      });
    }
    
    if (targetWalletIdForeignKey) {
      await queryInterface.addConstraint('chest_boosts', {
        fields: ['targetWalletId'],
        type: 'foreign key',
        name: targetWalletIdForeignKey.constraintName,
        references: {
          table: targetWalletIdForeignKey.referencedTableName,
          field: targetWalletIdForeignKey.referencedColumnName
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
      });
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('chest_boosts', 'targetTelegramId');

    // 获取表的外键约束
    const foreignKeys = await queryInterface.getForeignKeyReferencesForTable('chest_boosts');
    
    // 找到与targetUserId相关的外键约束
    const targetUserIdForeignKey = foreignKeys.find(fk => fk.columnName === 'targetUserId');
    if (targetUserIdForeignKey) {
      await queryInterface.removeConstraint('chest_boosts', targetUserIdForeignKey.constraintName);
    }
    
    // 找到与targetWalletId相关的外键约束
    const targetWalletIdForeignKey = foreignKeys.find(fk => fk.columnName === 'targetWalletId');
    if (targetWalletIdForeignKey) {
      await queryInterface.removeConstraint('chest_boosts', targetWalletIdForeignKey.constraintName);
    }

    // 恢复targetUserId和targetWalletId为不可空
    await queryInterface.changeColumn('chest_boosts', 'targetUserId', {
      type: Sequelize.INTEGER.UNSIGNED,
      allowNull: false
    });

    await queryInterface.changeColumn('chest_boosts', 'targetWalletId', {
      type: Sequelize.INTEGER.UNSIGNED,
      allowNull: false
    });
    
    // 重新添加外键约束（如果之前存在）
    if (targetUserIdForeignKey) {
      await queryInterface.addConstraint('chest_boosts', {
        fields: ['targetUserId'],
        type: 'foreign key',
        name: targetUserIdForeignKey.constraintName,
        references: {
          table: targetUserIdForeignKey.referencedTableName,
          field: targetUserIdForeignKey.referencedColumnName
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
      });
    }
    
    if (targetWalletIdForeignKey) {
      await queryInterface.addConstraint('chest_boosts', {
        fields: ['targetWalletId'],
        type: 'foreign key',
        name: targetWalletIdForeignKey.constraintName,
        references: {
          table: targetWalletIdForeignKey.referencedTableName,
          field: targetWalletIdForeignKey.referencedColumnName
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
      });
    }
  }
};