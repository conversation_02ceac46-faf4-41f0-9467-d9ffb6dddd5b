'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('free_ticket_transfers', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER.UNSIGNED
      },
      fromUserId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false
      },
      fromWalletId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false
      },
      toUserId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false
      },
      toWalletId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false
      },
      amount: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('free_ticket_transfers');
  }
};