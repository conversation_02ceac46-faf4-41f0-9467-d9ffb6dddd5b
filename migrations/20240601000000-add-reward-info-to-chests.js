'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // 检查表是否存在
      const tables = await queryInterface.showAllTables();
      if (!tables.includes('chests')) {
        console.log('找不到chests表，无法添加字段');
        return;
      }

      // 检查字段是否已存在
      const table = await queryInterface.describeTable('chests');
      if (table.rewardInfo) {
        console.log('rewardInfo字段已存在，跳过添加');
        return;
      }

      // 添加rewardInfo字段
      await queryInterface.addColumn('chests', 'rewardInfo', {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '宝箱奖励信息，包含等级和奖励项目详情'
      });

      console.log('成功添加rewardInfo字段到chests表');
    } catch (error) {
      console.error('添加rewardInfo字段失败:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      // 检查表是否存在
      const tables = await queryInterface.showAllTables();
      if (!tables.includes('chests')) {
        console.log('找不到chests表，无法回滚');
        return;
      }

      // 检查字段是否存在
      const table = await queryInterface.describeTable('chests');
      if (!table.rewardInfo) {
        console.log('rewardInfo字段不存在，跳过删除');
        return;
      }

      // 删除rewardInfo字段
      await queryInterface.removeColumn('chests', 'rewardInfo');
      console.log('成功删除rewardInfo字段');
    } catch (error) {
      console.error('删除rewardInfo字段失败:', error);
      throw error;
    }
  }
};