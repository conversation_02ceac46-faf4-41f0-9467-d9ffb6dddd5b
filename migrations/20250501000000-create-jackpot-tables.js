'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tables = await queryInterface.showAllTables();
    
    // 创建jackpot_pools表
    if (!tables.includes('jackpot_pools')) {
      await queryInterface.createTable('jackpot_pools', {
        id: {
          type: Sequelize.INTEGER.UNSIGNED,
          autoIncrement: true,
          primaryKey: true
        },
        level: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false
        },
        currentAmount: {
          type: Sequelize.DECIMAL(18, 6),
          defaultValue: 0,
          allowNull: false
        },
        targetAmount: {
          type: Sequelize.DECIMAL(18, 6),
          allowNull: false
        },
        newUserAmount: {
          type: Sequelize.DECIMAL(18, 6),
          defaultValue: 0,
          allowNull: false
        },
        chestOpenAmount: {
          type: Sequelize.DECIMAL(18, 6),
          defaultValue: 0,
          allowNull: false
        },
        lastWinnerId: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: true
        },
        lastWinnerWalletId: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: true
        },
        lastWinTime: {
          type: Sequelize.DATE,
          allowNull: true
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false
        }
      });
    }

    // 创建chest_countdowns表
    if (!tables.includes('chest_countdowns')) {
      await queryInterface.createTable('chest_countdowns', {
        id: {
          type: Sequelize.INTEGER.UNSIGNED,
          autoIncrement: true,
          primaryKey: true
        },
        userId: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false
        },
        walletId: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false
        },
        nextAvailableTime: {
          type: Sequelize.DATE,
          allowNull: false
        },
        autoCollect: {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
          allowNull: false
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false
        }
      });
    }

    // 创建chest_boosts表
    if (!tables.includes('chest_boosts')) {
      await queryInterface.createTable('chest_boosts', {
        id: {
          type: Sequelize.INTEGER.UNSIGNED,
          autoIncrement: true,
          primaryKey: true
        },
        sourceUserId: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false
        },
        sourceWalletId: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false
        },
        targetUserId: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false
        },
        targetWalletId: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false
        },
        boostType: {
          type: Sequelize.STRING,
          allowNull: false
        },
        boostMinutes: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false
        },
        chestLevel: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false
        },
        gemAmount: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false
        },
        isProcessed: {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
          allowNull: false
        },
        expiresAt: {
          type: Sequelize.DATE,
          allowNull: false
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false
        }
      });
    }

    // 创建share_boost_links表
    if (!tables.includes('share_boost_links')) {
      await queryInterface.createTable('share_boost_links', {
        id: {
          type: Sequelize.INTEGER.UNSIGNED,
          autoIncrement: true,
          primaryKey: true
        },
        userId: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false
        },
        walletId: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false
        },
        chestId: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false
        },
        chestLevel: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false
        },
        code: {
          type: Sequelize.STRING,
          allowNull: false,
          unique: true
        },
        maxUses: {
          type: Sequelize.INTEGER.UNSIGNED,
          defaultValue: 12,
          allowNull: false
        },
        currentUses: {
          type: Sequelize.INTEGER.UNSIGNED,
          defaultValue: 0,
          allowNull: false
        },
        expiresAt: {
          type: Sequelize.DATE,
          allowNull: false
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false
        }
      });
    }

    // 添加索引（先检查索引是否存在）
    const checkAndAddIndex = async (tableName, fields, options = {}) => {
      const tableIndexes = await queryInterface.showIndex(tableName);
      const indexName = options.name || `${tableName}_${fields.join('_')}_idx`;
      const indexExists = tableIndexes.some(index => index.name === indexName);
      if (!indexExists) {
        await queryInterface.addIndex(tableName, fields, { ...options, name: indexName });
      }
    };

    await checkAndAddIndex('chest_countdowns', ['userId', 'walletId']);
    await checkAndAddIndex('chest_boosts', ['sourceUserId', 'targetUserId']);
    await checkAndAddIndex('chest_boosts', ['expiresAt']);
    await checkAndAddIndex('share_boost_links', ['code']);
    await checkAndAddIndex('share_boost_links', ['userId', 'walletId']);
    await checkAndAddIndex('share_boost_links', ['expiresAt']);
  },

  down: async (queryInterface, Sequelize) => {
    const tables = await queryInterface.showAllTables();
    
    if (tables.includes('share_boost_links')) {
      await queryInterface.dropTable('share_boost_links');
    }
    if (tables.includes('chest_boosts')) {
      await queryInterface.dropTable('chest_boosts');
    }
    if (tables.includes('chest_countdowns')) {
      await queryInterface.dropTable('chest_countdowns');
    }
    if (tables.includes('jackpot_pools')) {
      await queryInterface.dropTable('jackpot_pools');
    }
  }
};