'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // 先检查字段是否存在
    const table = await queryInterface.describeTable('chests');
    if (!table.source) {
      await queryInterface.addColumn('chests', 'source', {
        type: Sequelize.STRING,
        allowNull: true,
        comment: '宝箱来源，例如：premium_referral, normal_referral'
      });
    }
  },

  async down(queryInterface, Sequelize) {
    // 检查字段是否存在后再删除
    const table = await queryInterface.describeTable('chests');
    if (table.source) {
      await queryInterface.removeColumn('chests', 'source');
    }
  }
};