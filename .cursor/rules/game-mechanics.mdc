---
description: 
globs: 
alwaysApply: false
---
# 游戏机制指南

## 核心游戏系统
- [src/routes/game.ts](mdc:src/routes/game.ts) - 主游戏逻辑
- [src/routes/gameLoopRoutes.ts](mdc:src/routes/gameLoopRoutes.ts) - 游戏循环机制
- [src/README-core-game-loop.md](mdc:src/README-core-game-loop.md) - 游戏循环文档

## 农场系统
- [src/routes/farmPlotRoutes.ts](mdc:src/routes/farmPlotRoutes.ts) - 农场地块API
- [src/models/FarmPlot.ts](mdc:src/models/FarmPlot.ts) - 农场地块模型
- [src/README-farm-plot.md](mdc:src/README-farm-plot.md) - 农场系统文档

## 配送系统
- [src/routes/deliveryLineRoutes.ts](mdc:src/routes/deliveryLineRoutes.ts) - 配送线API
- [src/models/DeliveryLine.ts](mdc:src/models/DeliveryLine.ts) - 配送线模型
- [src/README-delivery-line.md](mdc:src/README-delivery-line.md) - 配送系统文档

## 宝箱系统
- [src/routes/chestRoutes.ts](mdc:src/routes/chestRoutes.ts) - 普通宝箱
- [src/routes/jackpotChestRoutes.ts](mdc:src/routes/jackpotChestRoutes.ts) - 彩池宝箱
- [src/models/Chest.ts](mdc:src/models/Chest.ts) - 宝箱基础模型
- [src/models/ChestBoost.ts](mdc:src/models/ChestBoost.ts) - 宝箱加速
- [src/models/JackpotPool.ts](mdc:src/models/JackpotPool.ts) - 彩池模型

## 任务系统
- [src/routes/taskRoutes.ts](mdc:src/routes/taskRoutes.ts) - 任务API
- [src/models/Tasks.ts](mdc:src/models/Tasks.ts) - 任务配置
- [src/models/UserTaskComplete.ts](mdc:src/models/UserTaskComplete.ts) - 任务完成记录

## 奖励系统
- [src/routes/rewards.ts](mdc:src/routes/rewards.ts) - 奖励API
- [src/routes/dailyClaimRoutes.ts](mdc:src/routes/dailyClaimRoutes.ts) - 每日签到
- [src/models/Reward.ts](mdc:src/models/Reward.ts) - 奖励配置
- [src/models/RewardClaim.ts](mdc:src/models/RewardClaim.ts) - 奖励领取

## 特殊功能
- [src/routes/bullKingRoutes.ts](mdc:src/routes/bullKingRoutes.ts) - 牛王系统
- [src/routes/fragmentRoutes.ts](mdc:src/routes/fragmentRoutes.ts) - 碎片系统
- [src/routes/ticketRoutes.ts](mdc:src/routes/ticketRoutes.ts) - 门票系统
- [src/routes/freeTicketTransferRoutes.ts](mdc:src/routes/freeTicketTransferRoutes.ts) - 免费门票转移

## 游戏会话管理
- [src/models/Room.ts](mdc:src/models/Room.ts) - 游戏房间
- [src/models/Round.ts](mdc:src/models/Round.ts) - 游戏回合
- [src/models/Session.ts](mdc:src/models/Session.ts) - 游戏会话
- [src/models/GameHistory.ts](mdc:src/models/GameHistory.ts) - 游戏历史

## 定时任务
- [src/jobs/scheduleDailyRoundJobs.ts](mdc:src/jobs/scheduleDailyRoundJobs.ts) - 每日回合任务
- [src/scheduler/dailySessions.ts](mdc:src/scheduler/dailySessions.ts) - 每日会话调度
