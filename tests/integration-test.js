// tests/integration-test.js
// 集成测试：验证修复后的系统整体功能

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔄 循环依赖修复集成测试');
console.log('================================');

// 测试编译过程
async function testCompilation() {
  console.log('\n📦 测试编译过程...');
  
  return new Promise((resolve, reject) => {
    const compile = spawn('npm', ['run', 'build:dev'], {
      stdio: 'pipe',
      cwd: process.cwd()
    });

    let output = '';
    let errorOutput = '';

    compile.stdout.on('data', (data) => {
      output += data.toString();
    });

    compile.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    compile.on('close', (code) => {
      if (code === 0) {
        console.log('  ✅ TypeScript 编译成功');
        resolve(true);
      } else {
        console.log('  ❌ TypeScript 编译失败');
        console.log('  错误输出:', errorOutput);
        resolve(false);
      }
    });

    // 设置超时
    setTimeout(() => {
      compile.kill();
      console.log('  ⏰ 编译超时');
      resolve(false);
    }, 60000); // 60秒超时
  });
}

// 测试模块导入
async function testModuleImports() {
  console.log('\n📥 测试模块导入...');
  
  try {
    // 测试新服务的导入
    const VipEffectsService = require('../dist/services/vipEffectsService').default;
    const BoosterEffectsService = require('../dist/services/boosterEffectsService').default;
    
    console.log('  ✅ VipEffectsService 导入成功');
    console.log('  ✅ BoosterEffectsService 导入成功');
    
    // 测试控制器导入
    const iapController = require('../dist/controllers/iapController').default;
    console.log('  ✅ iapController 导入成功');
    
    // 测试其他服务导入
    const farmPlotService = require('../dist/services/farmPlotService').default;
    const deliveryLineService = require('../dist/services/deliveryLineService').default;
    const batchResourceUpdateService = require('../dist/services/batchResourceUpdateService').default;
    
    console.log('  ✅ farmPlotService 导入成功');
    console.log('  ✅ deliveryLineService 导入成功');
    console.log('  ✅ batchResourceUpdateService 导入成功');
    
    return true;
  } catch (error) {
    console.log('  ❌ 模块导入失败:', error.message);
    return false;
  }
}

// 测试方法调用
async function testMethodCalls() {
  console.log('\n🔧 测试方法调用...');
  
  try {
    const VipEffectsService = require('../dist/services/vipEffectsService').default;
    const BoosterEffectsService = require('../dist/services/boosterEffectsService').default;
    const iapController = require('../dist/controllers/iapController').default;
    
    // 测试VIP效果方法
    console.log('  📞 测试 VipEffectsService.getVipEffects...');
    try {
      const vipResult = await VipEffectsService.getVipEffects(999999);
      console.log('    ✅ 返回结构正确:', Object.keys(vipResult));
    } catch (error) {
      if (error.message.includes('database') || error.message.includes('connection')) {
        console.log('    ⚠️  数据库连接错误（预期的）');
      } else {
        throw error;
      }
    }
    
    // 测试加速器效果方法
    console.log('  📞 测试 BoosterEffectsService.getBoosterEffects...');
    try {
      const boosterResult = await BoosterEffectsService.getBoosterEffects(999999);
      console.log('    ✅ 返回结构正确:', Object.keys(boosterResult));
    } catch (error) {
      if (error.message.includes('database') || error.message.includes('connection')) {
        console.log('    ⚠️  数据库连接错误（预期的）');
      } else {
        throw error;
      }
    }
    
    // 测试控制器方法
    console.log('  📞 测试 iapController 方法...');
    try {
      const controllerVipResult = await iapController.getVipEffects(999999);
      const controllerBoosterResult = await iapController.getBoosterEffects(999999);
      console.log('    ✅ 控制器方法调用成功');
    } catch (error) {
      if (error.message.includes('database') || error.message.includes('connection')) {
        console.log('    ⚠️  数据库连接错误（预期的）');
      } else {
        throw error;
      }
    }
    
    return true;
  } catch (error) {
    console.log('  ❌ 方法调用测试失败:', error.message);
    return false;
  }
}

// 测试文件结构
function testFileStructure() {
  console.log('\n📁 测试文件结构...');
  
  const requiredFiles = [
    'src/services/vipEffectsService.ts',
    'src/services/boosterEffectsService.ts',
    'dist/services/vipEffectsService.js',
    'dist/services/boosterEffectsService.js'
  ];
  
  let allExist = true;
  requiredFiles.forEach(file => {
    const exists = fs.existsSync(file);
    console.log(`  ${exists ? '✅' : '❌'} ${file} ${exists ? '存在' : '不存在'}`);
    if (!exists) allExist = false;
  });
  
  return allExist;
}

// 测试代码质量
function testCodeQuality() {
  console.log('\n🔍 测试代码质量...');
  
  const serviceFiles = [
    'src/services/vipEffectsService.ts',
    'src/services/boosterEffectsService.ts'
  ];
  
  let qualityOk = true;
  serviceFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      
      // 检查是否有适当的错误处理
      const hasErrorHandling = content.includes('try') && content.includes('catch');
      console.log(`  ${hasErrorHandling ? '✅' : '❌'} ${file} 包含错误处理`);
      
      // 检查是否有接口定义
      const hasInterface = content.includes('interface ');
      console.log(`  ${hasInterface ? '✅' : '❌'} ${file} 包含接口定义`);
      
      // 检查是否有适当的注释
      const hasComments = content.includes('/**') || content.includes('//');
      console.log(`  ${hasComments ? '✅' : '❌'} ${file} 包含注释`);
      
      if (!hasErrorHandling || !hasInterface || !hasComments) {
        qualityOk = false;
      }
    }
  });
  
  return qualityOk;
}

// 主测试函数
async function runIntegrationTest() {
  console.log('开始集成测试...\n');
  
  const results = {
    compilation: false,
    fileStructure: false,
    moduleImports: false,
    methodCalls: false,
    codeQuality: false
  };
  
  // 1. 测试文件结构
  results.fileStructure = testFileStructure();
  
  // 2. 测试编译
  results.compilation = await testCompilation();
  
  // 3. 测试模块导入
  if (results.compilation) {
    results.moduleImports = await testModuleImports();
  }
  
  // 4. 测试方法调用
  if (results.moduleImports) {
    results.methodCalls = await testMethodCalls();
  }
  
  // 5. 测试代码质量
  results.codeQuality = testCodeQuality();
  
  // 输出测试结果
  console.log('\n📊 集成测试结果总结:');
  console.log('==================');
  
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? '通过' : '失败'}`);
  });
  
  const allPassed = Object.values(results).every(result => result);
  
  console.log('\n🎯 总体结果:');
  if (allPassed) {
    console.log('✅ 所有集成测试通过！');
    console.log('🚀 循环依赖修复成功，系统可以正常运行。');
  } else {
    console.log('❌ 部分集成测试失败');
    console.log('🔧 请检查失败的测试项目并进行修复。');
  }
  
  return allPassed;
}

// 运行测试
if (require.main === module) {
  runIntegrationTest().catch(console.error);
}

module.exports = { runIntegrationTest };
