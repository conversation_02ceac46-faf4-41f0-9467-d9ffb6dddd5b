// tests/circular-dependency-fix.test.js
// 单元测试：验证循环依赖修复效果

const { describe, it, expect, beforeAll } = require('@jest/globals');
const path = require('path');
const fs = require('fs');

describe('循环依赖修复测试', () => {
  let VipEffectsService;
  let BoosterEffectsService;
  let iapController;

  beforeAll(async () => {
    // 设置测试环境
    process.env.NODE_ENV = 'test';
    
    try {
      // 导入修复后的服务
      VipEffectsService = require('../dist/services/vipEffectsService').default;
      BoosterEffectsService = require('../dist/services/boosterEffectsService').default;
      iapController = require('../dist/controllers/iapController').default;
    } catch (error) {
      console.warn('无法导入编译后的文件，可能需要先运行 npm run build:dev');
      throw error;
    }
  });

  describe('新服务文件存在性测试', () => {
    it('应该创建 VipEffectsService 文件', () => {
      expect(fs.existsSync('src/services/vipEffectsService.ts')).toBe(true);
    });

    it('应该创建 BoosterEffectsService 文件', () => {
      expect(fs.existsSync('src/services/boosterEffectsService.ts')).toBe(true);
    });

    it('应该编译生成对应的 JS 文件', () => {
      expect(fs.existsSync('dist/services/vipEffectsService.js')).toBe(true);
      expect(fs.existsSync('dist/services/boosterEffectsService.js')).toBe(true);
    });
  });

  describe('循环依赖消除测试', () => {
    const serviceFiles = [
      'src/services/farmPlotService.ts',
      'src/services/deliveryLineService.ts',
      'src/services/batchResourceUpdateService.ts'
    ];

    serviceFiles.forEach(file => {
      it(`${file} 应该不再包含循环依赖`, () => {
        if (fs.existsSync(file)) {
          const content = fs.readFileSync(file, 'utf8');
          expect(content).not.toContain("require('../controllers/iapController')");
        }
      });

      it(`${file} 应该正确导入新服务`, () => {
        if (fs.existsSync(file)) {
          const content = fs.readFileSync(file, 'utf8');
          expect(content).toContain("import VipEffectsService from './vipEffectsService'");
          expect(content).toContain("import BoosterEffectsService from './boosterEffectsService'");
        }
      });
    });
  });

  describe('VipEffectsService 功能测试', () => {
    it('应该正确导出 VipEffectsService', () => {
      expect(VipEffectsService).toBeDefined();
      expect(typeof VipEffectsService).toBe('object');
    });

    it('应该包含所有必需的方法', () => {
      const requiredMethods = [
        'getVipEffects',
        'isVipMember', 
        'getVipMembership'
      ];

      requiredMethods.forEach(method => {
        expect(typeof VipEffectsService[method]).toBe('function');
      });
    });

    it('getVipEffects 应该返回正确的默认结构', async () => {
      try {
        const result = await VipEffectsService.getVipEffects(999999); // 使用不存在的ID
        
        expect(result).toHaveProperty('isVip');
        expect(result).toHaveProperty('deliverySpeedMultiplier');
        expect(result).toHaveProperty('blockPriceMultiplier');
        expect(result).toHaveProperty('productionSpeedMultiplier');
        
        // 默认值应该是非VIP状态
        expect(result.isVip).toBe(false);
        expect(result.deliverySpeedMultiplier).toBe(1);
        expect(result.blockPriceMultiplier).toBe(1);
        expect(result.productionSpeedMultiplier).toBe(1);
      } catch (error) {
        // 如果是数据库连接错误，这是预期的
        expect(error.message).toMatch(/database|connection|ECONNREFUSED/i);
      }
    });
  });

  describe('BoosterEffectsService 功能测试', () => {
    it('应该正确导出 BoosterEffectsService', () => {
      expect(BoosterEffectsService).toBeDefined();
      expect(typeof BoosterEffectsService).toBe('object');
    });

    it('应该包含所有必需的方法', () => {
      const requiredMethods = [
        'getBoosterEffects',
        'getSpeedMultiplier',
        'hasTimeWarpEffect',
        'getActiveBoosters',
        'hasActiveBoosterOfType'
      ];

      requiredMethods.forEach(method => {
        expect(typeof BoosterEffectsService[method]).toBe('function');
      });
    });

    it('getBoosterEffects 应该返回正确的默认结构', async () => {
      try {
        const result = await BoosterEffectsService.getBoosterEffects(999999); // 使用不存在的ID
        
        expect(result).toHaveProperty('speedMultiplier');
        expect(result).toHaveProperty('hasTimeWarp');
        
        // 默认值应该是无加速器状态
        expect(result.speedMultiplier).toBe(1);
        expect(result.hasTimeWarp).toBe(false);
      } catch (error) {
        // 如果是数据库连接错误，这是预期的
        expect(error.message).toMatch(/database|connection|ECONNREFUSED/i);
      }
    });
  });

  describe('iapController 兼容性测试', () => {
    it('应该保持原有的方法签名', () => {
      expect(typeof iapController.getVipEffects).toBe('function');
      expect(typeof iapController.getBoosterEffects).toBe('function');
    });

    it('getVipEffects 应该返回与之前相同的数据结构', async () => {
      try {
        const result = await iapController.getVipEffects(999999);
        
        expect(result).toHaveProperty('isVip');
        expect(result).toHaveProperty('deliverySpeedMultiplier');
        expect(result).toHaveProperty('blockPriceMultiplier');
        expect(result).toHaveProperty('productionSpeedMultiplier');
      } catch (error) {
        // 数据库连接错误是预期的
        expect(error.message).toMatch(/database|connection|ECONNREFUSED/i);
      }
    });

    it('getBoosterEffects 应该返回与之前相同的数据结构', async () => {
      try {
        const result = await iapController.getBoosterEffects(999999);
        
        expect(result).toHaveProperty('speedMultiplier');
        expect(result).toHaveProperty('hasTimeWarp');
      } catch (error) {
        // 数据库连接错误是预期的
        expect(error.message).toMatch(/database|connection|ECONNREFUSED/i);
      }
    });
  });

  describe('架构原则验证', () => {
    it('服务层不应该依赖控制器层', () => {
      const serviceFiles = [
        'src/services/farmPlotService.ts',
        'src/services/deliveryLineService.ts', 
        'src/services/batchResourceUpdateService.ts',
        'src/services/vipEffectsService.ts',
        'src/services/boosterEffectsService.ts'
      ];

      serviceFiles.forEach(file => {
        if (fs.existsSync(file)) {
          const content = fs.readFileSync(file, 'utf8');
          // 检查是否有导入控制器的语句
          expect(content).not.toMatch(/import.*from.*controllers/);
          expect(content).not.toMatch(/require.*controllers/);
        }
      });
    });

    it('新服务应该只依赖模型层', () => {
      const newServiceFiles = [
        'src/services/vipEffectsService.ts',
        'src/services/boosterEffectsService.ts'
      ];

      newServiceFiles.forEach(file => {
        if (fs.existsSync(file)) {
          const content = fs.readFileSync(file, 'utf8');
          // 应该只导入模型和工具
          const imports = content.match(/import.*from.*/g) || [];
          imports.forEach(importLine => {
            expect(importLine).toMatch(/models|sequelize|dayjs|Op/);
          });
        }
      });
    });
  });
});
