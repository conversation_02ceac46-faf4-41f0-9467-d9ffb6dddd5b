const crypto = require('crypto');

// 生成指定范围内的随机整数
function randomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 生成指定字节数的随机十六进制字符串
function randomHex(byteLength) {
  return crypto.randomBytes(byteLength).toString('hex');
}

// URL 编码 JSON 数据
function encodeUserData(userObj) {
  return encodeURIComponent(JSON.stringify(userObj));
}

// 从数组中随机选取一个元素
function randomChoice(arr) {
  return arr[randomInt(0, arr.length - 1)];
}

function generateInitData() {
  // 定义一些随机的名字数组
  const firstNames = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"];
  const lastNames = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"];

  // 随机选取 first_name 和 last_name
  const firstName = randomChoice(firstNames);
  const lastName = randomChoice(lastNames);
  // 使用 first_name 和 last_name 拼接生成 username
  const username = firstName + lastName;
  
  // 随机生成用户 id（10 位数字）
  const userId = randomInt(1000000000, 9999999999);
  const user = {
    id: userId,
    first_name: firstName,
    last_name: lastName,
    username: username,
    language_code: "zh-hans",
    allows_write_to_pm: true
  };

  const encodedUser = encodeUserData(user);

  // 随机生成 chat_instance（负数）
  const chatInstance = -randomInt(1000000000000000000, 9999999999999999999);
  // 固定 chat_type 为 private
  const chatType = "private";
  // 随机生成 auth_date（Unix 时间戳，范围在 2020~2027 年之间）
  const authDate = randomInt(1600000000, 1800000000);
  // 生成 32 字节（64 位十六进制字符）的随机 hash
  const hash = randomHex(32);

  // 生成最终的 initData 字符串
  const initData = `user=${encodedUser}&chat_instance=${chatInstance}&chat_type=${chatType}&auth_date=${authDate}&hash=${hash}`;
  return initData;
}

// 输出随机生成的 tg initData 数据
console.log(generateInitData());