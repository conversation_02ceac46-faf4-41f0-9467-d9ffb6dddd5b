# 任务：核心游戏循环（Core Loop）实现

## 任务描述
实现Moofun奶牛农场游戏的核心循环（Core Loop），将牧场区系统和出货线系统整合成完整的游戏体验。确保玩家能够通过养牛产奶、出售牛奶获得宝石、升级设施的循环获得持续的游戏乐趣。

## 功能需求

1. **核心循环流程**
   - 养牛产奶：牧场区自动生产牛奶
   - 出售牛奶：出货线自动将牛奶打包成方块并出售获得宝石
   - 升级设施：使用宝石升级牧场区和出货线
   - 解锁新牧场：使用宝石解锁新的牧场区
   - 持续累积产出：提升产能和收益

2. **游戏平衡**
   - 确保初始阶段玩家能够快速获得成就感
   - 中期阶段提供多样化的升级选择
   - 后期阶段保持足够的挑战性和成长空间

3. **经济系统**
   - 牛奶作为中间资源，不可直接消费
   - 宝石（GEM）作为主要货币，用于所有升级和解锁
   - 确保宝石收入与支出的平衡

4. **离线收益**
   - 玩家离线期间继续累积牛奶和宝石
   - 返回游戏时显示离线收益汇总

## 技术实现要点

1. **系统整合**
   - 整合牧场区系统和出货线系统
   - 实现资源流转逻辑（牛奶→宝石→升级）
   - 确保各系统间数据一致性

2. **游戏状态管理**
   - 实现游戏状态的保存和加载
   - 确保离线时间的正确计算
   - 实现离线收益的计算和展示

3. **用户界面**
   - 设计主界面，展示核心资源和设施
   - 实现资源变化的实时更新和动画效果
   - 设计排行榜界面

4. **数据分析**
   - 实现玩家行为数据收集
   - 设计关键指标监控（留存率、活跃度等）
   - 为游戏平衡调整提供数据支持

## 验收标准

1. 核心游戏循环流畅运行，各系统间数据流转正确
2. 游戏平衡性良好，玩家能够获得持续的成长感
3. 离线收益计算准确，系统能够正确处理离线时间
4. API接口设计合理，提供完整的数据访问能力
5. 性能良好，支持高并发访问

## 优先级
最高 - 整合所有系统的核心功能