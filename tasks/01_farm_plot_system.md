# 任务：牧场区（Farm Plot）系统实现

## 任务描述
实现牧场区（Farm Plot）系统，作为Moofun奶牛农场游戏的核心生产设施。玩家通过升级牧场区来增加牛舍数量，提高牛奶产量和产出速度。

## 功能需求

1. **牧场区基础属性**
   - 编号：1-20号牧场区
   - 等级：每个牧场区可升级至20级
   - 牛舍数量：等级对应的牛舍数量（1-20个）
   - 产量：每次产出获得的牛奶量
   - 速度：每次产出所需时间（秒）
   - 解锁费用：解锁新牧场区所需的GEM
   - 升级费用：提升牧场区等级所需的GEM

2. **初始设定**
   - 编号1的牧场区默认解锁（等级1）
   - 初始牛舍数量：1个
   - 初始产量：1牛奶/次
   - 初始速度：5秒/次
   - 初始升级费用：200 GEM
   - 其他牧场区解锁费用：2000 GEM起

3. **升级机制**
   - 每次升级提升牧场区等级+1
   - 每次升级增加牛舍数量+1
   - 每次升级提升产出速度5%（秒数 / 1.05）
   - 每次升级提升产量1.5倍
   - 每次升级提升下次升级费用1.5倍

4. **解锁机制**
   - 解锁新牧场区需要支付GEM
   - 每次解锁提升基础产量2.0倍
   - 每次解锁提升下次解锁费用2.0倍

## 技术实现要点

1. **数据模型设计**
   - 创建FarmPlot模型，包含所有必要属性
   - 设计用户与牧场区的关联关系
   - 实现牧场区状态的持久化存储

2. **升级与解锁逻辑**
   - 实现升级计算公式
   - 实现解锁新牧场区的逻辑
   - 确保GEM消费正确记录

3. **生产循环系统**
   - 实现基于时间的牛奶生产循环
   - 确保离线时间的产量计算准确
   - 实现牛奶存储与累积机制

4. **API设计**
   - 设计牧场区数据接口
   - 实现升级和解锁接口
   - 提供牧场区状态和产出信息查询接口

## 验收标准

1. 牧场区系统功能完整，包括升级、解锁和生产循环
2. 升级和解锁机制正确计算并应用
3. 牛奶生产循环正常运行，产出计算准确
4. API接口设计合理，提供完整的数据访问能力
5. 系统能够正确处理边界情况（如资源不足）
6. 性能良好，支持高并发访问

## 优先级
高 - 作为游戏核心生产系统