# 宝箱加速 API 文档

## 概述

该API允许用户手动加速24小时宝箱的倒计时。用户可以通过此功能减少等待时间，更快地领取宝箱奖励。

## 使用限制

- 单次加速最多10秒
- 每天累计最多加速2小时（7200秒）
- 需要用户登录并通过钱包认证

## API 端点

### 1. 加速宝箱倒计时

**请求**

```
POST /api/jackpot-chest/accelerate
```

**请求头**

```
Authorization: Bearer {token}
```

**请求体**

```json
{
  "seconds": 10  // 要加速的秒数，最大值为10
}
```

**响应**

```json
{
  "ok": true,
  "message": "宝箱倒计时已加速",
  "data": {
    "acceleratedSeconds": 10,  // 实际加速的秒数
    "newNextAvailableTime": "2023-05-15T10:30:00Z",  // 新的可领取时间
    "totalAcceleratedToday": 120,  // 今日已使用的总加速秒数
    "remainingAccelerationSeconds": 7080  // 今日剩余可用加速秒数
  }
}
```

**错误响应**

```json
{
  "ok": false,
  "message": "请提供有效的加速秒数"  // 或其他错误信息
}
```

### 2. 获取用户的宝箱加速状态

**请求**

```
GET /api/jackpot-chest/acceleration-status
```

**请求头**

```
Authorization: Bearer {token}
```

**响应**

```json
{
  "ok": true,
  "data": {
    "usedAccelerationSeconds": 120,  // 今日已使用的加速秒数
    "remainingAccelerationSeconds": 7080,  // 今日剩余可用加速秒数
    "maxAccelerationPerDay": 7200,  // 每天最大加速秒数
    "maxAccelerationPerTime": 10  // 单次最大加速秒数
  }
}
```

## 注意事项

- 加速操作会立即生效，减少宝箱的倒计时
- 如果加速后倒计时变为0，用户可以立即领取宝箱
- 加速限制会在每天0点重置
- 请合理使用加速功能，避免在短时间内频繁请求