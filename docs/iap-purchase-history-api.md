# IAP 购买历史查询 API

## 接口信息

- **路径**: `/api/iap/purchase/history`
- **方法**: GET
- **描述**: 获取用户的IAP商品购买历史记录，支持分页查询
- **认证**: 需要钱包认证，请求头中必须包含 `Authorization: Bearer <token>`

## 请求参数

### Body参数

```json
{
  "page": 1,        // 页码，默认为1（可选）
  "limit": 20       // 每页记录数，默认为20（可选）
}
```

#### 参数说明

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|---------|
| page | number | 否 | 1 | 页码，从1开始 |
| limit | number | 否 | 20 | 每页返回的记录数量 |

## 响应参数

### 成功响应

**状态码**: 200

```json
{
  "ok": true,
  "purchases": [
    {
      "id": 1,
      "walletId": 123,
      "productId": 1,
      "paymentId": "payment_123456789",
      "status": "completed",
      "paymentMethod": "kaia",
      "amount": 8.7719,
      "currency": "KAIA",
      "purchaseDate": "2024-01-15T10:30:00.000Z",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:35:00.000Z",
      "IapProduct": {
        "name": "速度提升道具 x2 1小时",
        "type": "speed_boost",
        "description": "提升游戏速度2倍，持续1小时"
      }
    },
    {
      "id": 2,
      "walletId": 123,
      "productId": 5,
      "paymentId": "payment_987654321",
      "status": "pending",
      "paymentMethod": "stripe",
      "amount": 9.99,
      "currency": "USD",
      "purchaseDate": "2024-01-14T15:20:00.000Z",
      "createdAt": "2024-01-14T15:20:00.000Z",
      "updatedAt": "2024-01-14T15:20:00.000Z",
      "IapProduct": {
        "name": "VIP会员 30天",
        "type": "vip_membership",
        "description": "享受VIP特权30天"
      }
    }
  ],
  "pagination": {
    "total": 15,
    "page": 1,
    "limit": 20,
    "totalPages": 1
  }
}
```

#### 响应字段说明

**purchases数组字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 购买记录ID |
| walletId | number | 用户钱包ID |
| productId | number | 商品ID |
| paymentId | string | DappPortal支付ID |
| status | string | 支付状态：CREATED(已创建)、STARTED(已开始)、REGISTERED_ON_PG(已注册到支付网关)、CAPTURED(已捕获)、PENDING(待处理)、CONFIRMED(已确认)、FINALIZED(已完成)、REFUNDED(已退款)、CONFIRM_FAILED(确认失败)、CANCELED(已取消)、CHARGEBACK(退单) |
| paymentMethod | string | 支付方式：kaia(KAIA代币)、stripe(信用卡) |
| amount | number | 支付金额 |
| currency | string | 货币类型：USD、KAIA |
| purchaseDate | string | 购买时间 |
| createdAt | string | 记录创建时间 |
| updatedAt | string | 记录更新时间 |
| IapProduct | object | 商品信息 |

**IapProduct字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| name | string | 商品名称 |
| type | string | 商品类型：speed_boost、time_warp、vip_membership、special_offer |
| description | string | 商品描述 |

**pagination字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| total | number | 总记录数 |
| page | number | 当前页码 |
| limit | number | 每页记录数 |
| totalPages | number | 总页数 |