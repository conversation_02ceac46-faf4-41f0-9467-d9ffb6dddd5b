# Moofun 奶牛农场游戏系统需求文档

## 1. 项目概述

Moofun奶牛农场是一款基于区块链技术的放置类游戏，玩家通过管理牧场区和出货线，实现牛奶生产、加工和销售的完整经济循环。游戏核心玩法围绕"养牛产奶→出售牛奶获得宝石→升级设施→提升产能"的循环模式，为玩家提供持续的游戏乐趣和进步空间。

## 2. 核心系统

### 2.1 牧场区（Farm Plot）系统

#### 2.1.1 基本属性

- **总数量**：20个牧场区
- **等级上限**：每个牧场区可升级至20级
- **编号**：1-20号
- **牛舍数量**：1-20个（与等级相对应）
- **产量**：每次产出获得的牛奶量
- **速度**：每次产出所需时间（秒）
- **解锁费用**：解锁新牧场区所需的GEM
- **升级费用**：提升牧场区等级所需的GEM

#### 2.1.2 初始设定

- **编号1牧场区**：默认解锁（等级1）
- **初始牛舍数量**：1个
- **初始产量**：1牛奶/次
- **初始速度**：5秒/次
- **初始升级费用**：200 GEM（升级到2级需要）
- **初始解锁费用**：2000 GEM（从编号2开始）

#### 2.1.3 升级与解锁机制

- **等级提升**：每次升级 等级+1
- **牛舍增加**：每次升级 牛舍数量+1
- **产量增长**：每次升级 产量×1.5倍
- **速度提升**：每次升级 速度÷1.05（提升5%）
- **升级费用增长**：每次升级 升级费用×1.5倍
- **解锁费用增长**：每次解锁 解锁费用×2.0倍
- **解锁产量提升**：每次解锁 基础产量×2.0倍
#### 2.1.4 牧场区数值表（示例）

**牧场区#1（默认解锁）**

| 等级 | 升级费用(GEM) | 牛奶产量 | 生产速度(秒) | 牛舍数量 |
|------|---------------|----------|--------------|----------|
| 1    | 200           | 1.000    | 5.000        | 1        |
| 2    | 300           | 1.500    | 4.762        | 2        |
| 3    | 450           | 2.250    | 4.535        | 3        |
| 4    | 675           | 3.375    | 4.319        | 4        |
| 5    | 1,013         | 5.063    | 4.114        | 5        |

**牧场区#2（解锁费用：2000 GEM）**

| 等级 | 升级费用(GEM) | 牛奶产量 | 生产速度(秒) | 牛舍数量 |
|------|---------------|----------|--------------|----------|
| 1    | 400           | 2.000    | 5.000        | 1        |
| 2    | 600           | 3.000    | 4.762        | 2        |
| 3    | 900           | 4.500    | 4.535        | 3        |
| 4    | 1,350         | 6.750    | 4.319        | 4        |
| 5    | 2,025         | 10.125   | 4.114        | 5        |

**牧场区#3（解锁费用：4000 GEM）**

| 等级 | 升级费用(GEM) | 牛奶产量 | 生产速度(秒) | 牛舍数量 |
|------|---------------|----------|--------------|----------|
| 1    | 800           | 4.000    | 5.000        | 1        |
| 2    | 1,200         | 6.000    | 4.762        | 2        |
| 3    | 1,800         | 9.000    | 4.535        | 3        |
| 4    | 2,700         | 13.500   | 4.319        | 4        |
| 5    | 4,050         | 20.250   | 4.114        | 5        |
### 2.2 出货线（Delivery Line）系统

#### 2.2.1 基本属性

- **出货速度**：每次出货所需时间（秒）
- **方块单位**：每个牛奶方块包含的牛奶数量
- **方块价格**：每个牛奶方块出售获得的GEM数量
- **升级费用**：提升出货线等级所需的GEM

#### 2.2.2 初始设定

- **初始出货速度**：5秒/次
- **初始方块单位**：5牛奶/方块
- **初始方块价格**：5 GEM/方块
- **初始升级费用**：500 GEM

#### 2.2.3 升级机制

- **等级提升**：每次升级 等级+1
- **方块单位增长**：每次升级 方块单位×2.0倍
- **方块价格增长**：每次升级 方块价格×2.0倍
- **速度提升**：每次升级 出货速度÷1.01（提升1%）
- **升级费用增长**：每次升级 升级费用×2.0倍
## 3. 时间跳跃功能

### 3.1 功能概述

时间跳跃道具允许用户立即获得一段时间内的游戏收益，只影响农场部分，不影响倒计时宝箱等其他功能。

### 3.2 时间跳跃类型

- 1小时时间跳跃
- 4小时时间跳跃
- 8小时时间跳跃

### 3.3 计算逻辑

#### 3.3.1 基础计算

时间跳跃功能计算牧场区和出货线在指定时间内的完整收益：

- **时间内生产牛奶** = 所有牧场区每秒生产牛奶 × 跳跃时间（秒）
- **时间内可处理牛奶** = 出货线每秒处理牛奶 × 跳跃时间（秒）
- **实际处理牛奶** = min(时间内生产牛奶, 时间内可处理牛奶)

#### 3.3.2 收益计算

时间跳跃功能直接计算GEM收益：

- **获得GEM** = 实际处理牛奶 ÷ 方块单位 × 方块价格
- **最终收益** = GEM（直接添加到用户GEM余额）

#### 3.3.3 计算示例

假设用户使用1小时时间跳跃道具：

**当前状态：**
- 牧场区总产量：10牛奶/秒
- 出货线处理能力：8牛奶/秒
- 方块单位：5牛奶/方块
- 方块价格：10 GEM/方块
- 用户当前GEM：1000

**计算过程：**
1. 时间内生产牛奶 = 10 × 3600 = 36,000牛奶
2. 时间内可处理牛奶 = 8 × 3600 = 28,800牛奶
3. 实际处理牛奶 = min(36,000, 28,800) = 28,800牛奶
4. 获得GEM = 28,800 ÷ 5 × 10 = 57,600 GEM

**最终收益：**
- 获得GEM：57,600 GEM
- 用户GEM余额：1000 + 57,600 = 58,600 GEM
- 说明：时间跳跃计算牧场区和出货线的完整收益链，直接获得GEM

### 3.4 实现要点

1. 计算时间跳跃期间所有牧场区产生的牛奶总量
2. 计算出货线在同一时间内可处理的牛奶数量
3. 确定实际可转换为GEM的牛奶数量（取较小值）
4. 根据方块单位和方块价格计算获得的GEM
5. 直接将GEM收益添加到用户钱包
6. 更新农场区和出货线的最后操作时间
7. 返回获得的GEM数量

### 3.5 注意事项

- 时间跳跃模拟完整的生产→出货→获得GEM的流程
- 不会扣除用户现有资源，只增加GEM收益
- 需要更新农场区和出货线的最后操作时间
- 计算结果返回获得的GEM数量
- 时间跳跃不影响倒计时宝箱等其他游戏功能
- 如果牧场区产量超过出货线处理能力，按出货线能力计算GEM
## 4. 核心游戏循环

### 4.1 资源流转

**牛奶生产：牧场区自动产出牛奶**
- 产出量 = 牛舍数量 × 单个牛舍产量
- 产出频率 = 牧场区生产速度

**牛奶出售：出货线自动将牛奶打包成方块并出售**
- 打包速度 = 出货线速度
- 每个方块包含 = 方块单位数量的牛奶
- 每个方块售价 = 方块价格数量的GEM

**资源升级：玩家使用GEM升级牧场区和出货线**
- 提高牛奶产量和产出速度
- 提高牛奶方块价值和出货速度

### 4.2 离线收益

- 系统自动计算玩家离线期间的完整收益链
- 玩家返回游戏时可领取离线期间产生的GEM收益
- 离线收益计算逻辑与时间跳跃相同：模拟完整的生产→出货→获得GEM流程
## 5. 牧场区解锁费用参考表

| 牧场区编号 | 解锁费用(GEM) |
|------------|---------------|
| 1          | 0 (免费)      |
| 2          | 2,000         |
| 3          | 4,000         |
| 4          | 8,000         |
| 5          | 16,000        |
| 6          | 32,000        |
| 7          | 64,000        |
| 8          | 128,000       |
| 9          | 256,000       |
| 10         | 512,000       |
| ...        | ...           |
| 20         | 262,144,000   |
## 6. 数据模型

### 6.1 用户钱包
- 存储用户的牛奶和宝石资源
- 记录资源变动历史

### 6.2 牧场区
- 存储牧场区等级、产量、速度等属性
- 记录最后收集时间

### 6.3 出货线
- 存储出货线等级、方块单位、方块价格等属性
- 记录最后出货时间

## 7. 游戏平衡

- 高级牧场区解锁费用高，但产出效率也更高
- 升级费用随等级提高而增长，保持游戏平衡
- 出货线升级提高牛奶转化为宝石的效率
- 时间跳跃功能提供即时奖励，增强游戏体验

## 8. 技术实现注意事项

- 所有数值计算需要考虑精度问题，避免浮点数误差
- 资源变动需要使用事务确保数据一致性
- 时间相关计算需要考虑时区问题
- 离线收益和时间跳跃计算需要防止作弊行为
- 所有升级和解锁操作需要进行资源充足性验证


---

本文档详细描述了Moofun奶牛农场游戏的核心系统和功能，为开发团队提供了明确的实现指南。随着游戏开发的进展，本文档将继续更新和完善。