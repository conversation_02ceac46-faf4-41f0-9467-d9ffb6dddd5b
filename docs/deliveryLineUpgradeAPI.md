# 接口文档：升级出货线

## 1. 概述

该接口用于升级当前认证用户的出货线。升级会消耗用户的 GEM，并提升出货线的等级，从而可能改变出货速度、每方块牛奶量、方块价格以及下一次升级的费用。

## 2. 请求

-   **URL**: `/api/delivery/delivery-line/upgrade`
-   **方法**: `POST`
-   **认证**: 需要用户认证 

## 3. 请求体 (Request Body)

无。此接口不需要请求体。

## 4. 响应 (Response)

### 4.1. 成功响应

-   **状态码**: `200 OK`
-   **Content-Type**: `application/json`

**响应体结构:**

```json
{
  "ok": true,
  "data": {
    "id": "integer",
    "walletId": "integer",
    "level": "integer", // 升级后的等级
    "deliverySpeed": "number", // 升级后的出货速度 (秒/次, 3位小数精度)
    "blockUnit": "number", // 升级后的每个牛奶方块包含的牛奶数量 (3位小数精度)
    "blockPrice": "number", // 升级后的每个牛奶方块的价格 (GEM, 3位小数精度)
    "upgradeCost": "number", // 升级到下一等级所需的GEM
    "lastDeliveryTime": "string(date-time)",
    "pendingMilk": "number", // 待处理的牛奶数量
    "createdAt": "string(date-time)",
    "updatedAt": "string(date-time)",
    "nextUpgradeGrowth": {
      "nextDeliverySpeed": "number", // 下次升级后的出货速度 (3位小数精度)
      "nextBlockUnit": "number", // 下次升级后的方块容量 (3位小数精度)
      "nextBlockPrice": "number" // 下次升级后的方块价格 (3位小数精度)
    }
  }
}
```

**响应字段说明:**

-   `id`: 出货线记录的唯一ID
-   `walletId`: 用户钱包ID
-   `level`: 升级后出货线的当前等级
-   `deliverySpeed`: 升级后的出货速度（单位：秒/次，3位小数精度）。数值越小，速度越快
-   `blockUnit`: 升级后，每个牛奶方块包含的牛奶数量（3位小数精度）
-   `blockPrice`: 升级后，每个牛奶方块出售时能获得的GEM数量（3位小数精度）
-   `upgradeCost`: 升级到下一等级出货线所需的GEM数量
-   `lastDeliveryTime`: 上一次完成出货的时间戳
-   `pendingMilk`: 已经添加到出货线但尚未转换成牛奶方块的牛奶数量
-   `createdAt`: 出货线记录创建时间
-   `updatedAt`: 出货线记录最后更新时间（即本次升级的时间）
-   `nextUpgradeGrowth`: 下次升级预览数据对象，包含：
    -   `nextDeliverySpeed`: 下次升级后的出货速度（3位小数精度）
    -   `nextBlockUnit`: 下次升级后的方块容量（3位小数精度）
    -   `nextBlockPrice`: 下次升级后的方块价格（3位小数精度）

**升级规则说明:**

### 升级计算公式
-   **等级**: `level + 1`
-   **出货速度**: `deliverySpeed ÷ 1.01` (每次升级提升1%速度)
-   **方块容量**: `blockUnit × 2.0` (每次升级提升2倍)
-   **方块价格**: `blockPrice × 2.0` (每次升级提升2倍)
-   **升级费用**: `upgradeCost × 2.0` (每次升级提升2倍)

### nextUpgradeGrowth 说明
升级成功后，响应中的 `nextUpgradeGrowth` 对象包含再次升级的预览数据：
-   `nextDeliverySpeed`: 再次升级后的出货速度
-   `nextBlockUnit`: 再次升级后的方块容量
-   `nextBlockPrice`: 再次升级后的方块价格

这些数据帮助用户预览下次升级的效果，便于决策是否继续升级。

**示例成功响应:**

```json
{
  "ok": true,
  "data": {
    "id": 1,
    "walletId": 1,
    "level": 3,
    "deliverySpeed": 4.901,
    "blockUnit": 20.000,
    "blockPrice": 20.000,
    "upgradeCost": 2000,
    "lastDeliveryTime": "2025-06-18T10:00:00.000Z",
    "pendingMilk": 15,
    "createdAt": "2025-06-18 07:17:29",
    "updatedAt": "2025-06-18T10:15:00.000Z",
    "nextUpgradeGrowth": {
      "nextDeliverySpeed": 4.851,
      "nextBlockUnit": 40.000,
      "nextBlockPrice": 40.000
    }
  }
}
```

### 4.2. 错误响应

-   **状态码**: `400 Bad Request` 或 `500 Internal Server Error`
-   **Content-Type**: `application/json`

**常见错误情况:**

```json
{
  "ok": false,
  "error": "用户钱包或出货线不存在"
}
```

```json
{
  "ok": false,
  "error": "GEM不足，无法升级出货线"
}
```

## 5. 注意事项

-   升级出货线会消耗用户钱包中的 `GEM` 货币
-   升级后所有属性都会按照固定公式提升
-   所有数值使用BigNumber.js计算，确保3位小数精度
-   `pendingBlocks` 字段已从API响应中移除，前端可通过 `Math.floor(pendingMilk / blockUnit)` 计算
-   升级后立即返回包含 `nextUpgradeGrowth` 的完整数据，前端无需额外API调用
-   `nextUpgradeGrowth` 提供下次升级预览，帮助用户决策是否继续升级

## 6. 升级效果计算示例

假设当前出货线状态：
- 等级：2
- 出货速度：4.950秒
- 方块容量：10.000牛奶/方块
- 方块价格：10.000 GEM/方块
- 升级费用：1000 GEM

升级后的状态：
- 等级：3
- 出货速度：4.901秒 (4.950 ÷ 1.01)
- 方块容量：20.000牛奶/方块 (10.000 × 2.0)
- 方块价格：20.000 GEM/方块 (10.000 × 2.0)
- 升级费用：2000 GEM (1000 × 2.0)

下次升级预览：
- 出货速度：4.851秒 (4.901 ÷ 1.01)
- 方块容量：40.000牛奶/方块 (20.000 × 2.0)
- 方块价格：40.000 GEM/方块 (20.000 × 2.0)