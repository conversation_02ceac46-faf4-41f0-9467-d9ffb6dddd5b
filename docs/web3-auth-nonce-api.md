# Web3钱包获取Nonce接口

## 接口说明
获取用于Web3钱包登录的nonce值，该nonce将用于生成签名消息。

## 请求信息
- **路径**: `/api/web3-auth/nonce`
- **方法**: POST
- **Content-Type**: application/json

## 请求参数
### Body参数
```json
{
  "walletAddress": "string" // 钱包地址（必填）
}
```

## 响应结果
### 成功响应
```json
{
  "ok": true,
  "data": {
    "nonce": "string", // 随机生成的nonce值
    "message": "string" // 用于签名的消息内容
  }
}
```

## 注意事项
1. nonce值有效期为5分钟
2. 每次请求都会生成新的nonce值
3. 获取到的message需要使用钱包进行签名，用于后续的登录请求

## 示例
### 请求示例
```bash
curl -X POST http://your-domain/api/web3-auth/nonce \
  -H "Content-Type: application/json" \
  -d '{"walletAddress": "0x1234...5678"}'
```

### 响应示例
```json
{
  "ok": true,
  "data": {
    "nonce": "0ef99695-daaf-4f59-87d6-f5f2c0170423",
    "message": "Welcome to MooFun!\n\nPlease sign to verify your wallet address, after which you can begin your wonderful journey.\n\nSecurity code: 0ef99695-daaf-4f59-87d6-f5f2c0170423\nTimestamp: 1746599675551"
  }
}
```