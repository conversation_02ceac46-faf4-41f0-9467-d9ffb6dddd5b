# TON提现 API

## 提现TON代币

### 接口信息

- **路径**: `/api/withdrawal/ton`
- **方法**: POST
- **描述**: 将用户钱包中的TON代币提现到指定的钱包地址
- **认证**: 需要钱包认证，请求头中必须包含 `Authorization: Bearer <token>`

### 请求参数

#### 请求头

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| Authorization | string | 是 | Bearer 认证token |

#### 请求体

```json
{
  "amount": 1.5  // 提现金额，最小值为1
}
```

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| amount | number | 是 | 提现金额，必须大于等于1 |

### 响应参数

#### 成功响应

```json
{
  "ok": true,
  "data": {
    "amount": 1,
    "fee": 0.1,
    "status": "approved",
    "needManualApprove": false
  },
  "message": "success.withdrawTONSuccess"
}
```

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| amount | number | 提现金额 |
| fee | number | 提现手续费 |
| status | string | 提现状态，可能值："pending"(处理中)、"approved"(已批准)、"failed"(失败) |
| needManualApprove | boolean | 是否需要人工审核 |

#### 错误响应

```json
{
  "ok": false,
  "error": "提现TON失败",
  "details": "余额不足"
}
```

### 错误码

| 状态码 | 错误信息 | 描述 |
|--|--|--|
| 400 | 参数验证失败 | 当请求参数不符合要求时返回 |
| 400 | 钱包信息未找到 | 当用户钱包ID无效时返回 |
| 400 | 提现地址未找到 | 当无法获取用户钱包地址时返回 |
| 400 | 余额不足 | 当用户TON余额不足时返回 |
| 401 | 未授权 | 当用户未登录或token无效时返回 |
| 500 | 提现TON失败 | 当服务器处理提现请求时发生错误 |

### 业务逻辑说明

1. 系统会验证用户的TON余额是否足够进行提现
2. 提现金额必须大于等于1个TON
3. 如果用户未指定提现地址，系统将使用用户钱包的默认地址
4. 提现申请提交后，状态为"pending"，后台系统会异步处理提现请求
5. 提现完成后，状态会更新为"completed"或"failed"
6. 用户可以通过其他接口查询提现状态