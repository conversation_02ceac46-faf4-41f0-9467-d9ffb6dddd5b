# 宝箱记录 API

### 获取用户宝箱记录

**接口路径**：`GET /api/chest/my-chest-records`

**接口描述**：获取用户的宝箱记录列表，包括宝箱ID、类型、来源、开启时间、奖励信息等。支持分页查询。

**请求方法**：GET

**认证要求**：需要钱包认证

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|-----|------|------|
| page | Number | 否 | 页码，默认为1 |
| limit | Number | 否 | 每页记录数，默认为20 |

**响应结果**：

```json
{
  "ok": true,
  "data": {
    "records": [
      {
        "id": 9,
        "type": "countdown",
        "source": null,
        "isOpened": true,
        "openedAt": "2025-04-05 11:06:41",
        "rewardInfo": {
          "items": [
            {
              "type": "fragment_green",
              "amount": 17
            },
            {
              "type": "fragment_blue",
              "amount": 1
            },
            {
              "type": "gem",
              "amount": 1949
            }
          ],
          "level": 1
        },
        "createdAt": "2025-04-05 11:06:41",
        "updatedAt": "2025-04-05 11:06:41"
      },
      {
        "id": 7,
        "type": "countdown",
        "source": null,
        "isOpened": true,
        "openedAt": "2025-03-27 17:50:07",
        "rewardInfo": null,
        "createdAt": "2025-03-27 17:50:07",
        "updatedAt": "2025-03-27 17:50:07"
      },
      {
        "id": 1,
        "type": "referral",
        "source": "normal_referral",
        "isOpened": true,
        "openedAt": "2025-03-27 17:49:49",
        "rewardInfo": null,
        "createdAt": "2025-03-27 17:46:04",
        "updatedAt": "2025-03-27 17:49:49"
      }
      // 更多记录...
    ],
    "total": 4,
    "page": 1,
    "limit": 20,
    "totalPages": 1
  },
  "message": "success.getChestRecords"
}
```

**响应字段说明**：

| 字段名 | 类型 | 描述 |
|-------|-----|------|
| records | Array | 宝箱记录列表 |
| records[].id | Number | 宝箱记录ID |
| records[].type | String | 宝箱类型 |
| records[].source | String | 宝箱来源 |
| records[].isOpened | Boolean | 是否已开启 |
| records[].openedAt | String | 开启时间，日期字符串格式 |
| records[].rewardInfo | Object/null | 奖励信息，未开启或无奖励时为null |
| records[].rewardInfo.items | Array | 奖励物品列表 |
| records[].rewardInfo.items[].type | String | 奖励物品类型，如fragment_green、fragment_blue、gem等 |
| records[].rewardInfo.items[].amount | Number | 奖励物品数量 |
| records[].rewardInfo.level | Number | 奖励等级 |
| records[].createdAt | String | 创建时间，日期字符串格式 |
| records[].updatedAt | String | 更新时间，日期字符串格式 |
| total | Number | 记录总数 |
| page | Number | 当前页码 |
| limit | Number | 每页记录数 |
| totalPages | Number | 总页数 |

**错误响应**：

```json
{
  "ok": false,
  "message": "获取宝箱记录失败"
}
```

**错误码**：

| HTTP状态码 | 错误信息 | 描述 |
|-----------|---------|------|
| 400 | 获取宝箱记录失败 | 获取宝箱记录时发生错误 |
| 400 | 钱包ID不存在 | 用户钱包ID不存在 |
| 401 | 未授权 | 用户未登录或token无效 |