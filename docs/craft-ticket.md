# 碎片制作门票 API

## 接口信息

- **路径**: `/api/fragment/craft-ticket`
- **方法**: POST
- **描述**: 使用不同类型的碎片制作门票
- **认证**: 需要钱包认证，请求头中必须包含 `Authorization: Bearer <token>`

## 请求参数

### 请求头

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| Authorization | string | 是 | Bearer 认证令牌，格式为 `Bearer <token>` |

### 请求体

```json
{
  "fragmentType": "fragment_green",
  "quantity": 1
}
```

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| fragmentType | string | 是 | 碎片类型，可选值：`fragment_green`, `fragment_blue`, `fragment_purple`, `fragment_gold` |
| quantity | integer | 是 | 要使用的碎片组数量，必须大于等于1 |

## 碎片兑换比例

不同类型的碎片可以兑换的门票数量不同：

| 碎片类型 | 每组所需碎片数量 | 可兑换门票数量 |
| -------- | ---------------- | -------------- |
| fragment_green | 160 | 1 |
| fragment_blue | 80 | 3 |
| fragment_purple | 40 | 15 |
| fragment_gold | 20 | 60 |

## 响应结果

### 成功响应

```json
{
  "code": 0,
  "message": "成功使用碎片制作门票",
  "data": {
    "balance": {
      "ticket": 15,
      "fragment_green": 320,
      "fragment_blue": 160,
      "fragment_purple": 80,
      "fragment_gold": 40
    }
  }
}
```

| 字段 | 类型 | 描述 |
| ---- | ---- | ---- |
| code | integer | 状态码，0表示成功 |
| message | string | 响应消息 |
| data.balance | object | 更新后的钱包余额信息 |
| data.balance.ticket | integer | 更新后的门票余额 |
| data.balance.fragment_green | integer | 更新后的绿色碎片余额 |
| data.balance.fragment_blue | integer | 更新后的蓝色碎片余额 |
| data.balance.fragment_purple | integer | 更新后的紫色碎片余额 |
| data.balance.fragment_gold | integer | 更新后的金色碎片余额 |

### 错误响应

```json
{
  "code": 1,
  "message": "碎片数量不足",
  "errors": []
}
```

| 错误消息 | 描述 |
| -------- | ---- |
| 参数验证失败 | 请求参数不符合要求 |
| 无效的碎片类型 | 提供的碎片类型不在支持范围内 |
| 数量必须为正数 | quantity参数必须大于0 |
| 碎片数量不足 | 用户钱包中没有足够的碎片进行兑换 |

## 示例

### 请求示例

```bash
curl -X POST \
  https://api.example.com/api/fragment/craft-ticket \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "fragmentType": "fragment_purple",
    "quantity": 2
  }'
```

### 响应示例

```json
{
  "code": 0,
  "message": "成功使用碎片制作门票",
  "data": {
    "balance": {
      "ticket": 30,
      "fragment_green": 320,
      "fragment_blue": 160,
      "fragment_purple": 0,
      "fragment_gold": 40
    }
  }
}
```