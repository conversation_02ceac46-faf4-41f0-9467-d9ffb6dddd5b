# 重置任务完成状态 API 文档

本文档描述了用于重置用户任务完成状态的API接口。该接口仅用于测试环境，可以帮助开发人员和测试人员更方便地测试任务相关功能。

## 基本信息

- 基础路径: `/api/test-chest`
- 所有接口都需要用户身份验证（钱包认证和语言中间件）

## API 接口详情

### 重置任务完成状态

该接口用于删除用户的任务完成记录，使用户可以再次完成已完成的任务，便于测试任务完成和奖励机制。

**请求方法**: POST

**路径**: `/api/test-chest/reset-task-complete`

**请求头**:

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| Authorization | String | 是 | 用户认证令牌，格式为 `Bearer {token}` |

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| taskId | Number | 否 | 要重置的特定任务ID。如不提供，将重置所有任务的完成状态 |

**请求示例**:

```http
POST /api/test-chest/reset-task-complete HTTP/1.1
Host: api.example.com
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "taskId": 3
}
```

**重置所有任务示例**:

```http
POST /api/test-chest/reset-task-complete HTTP/1.1
Host: api.example.com
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**响应示例 (重置特定任务)**:

```json
{
  "ok": true,
  "message": "任务#3完成状态已重置",
  "data": {
    "userId": 123,
    "walletId": 456,
    "taskId": 3,
    "deletedRecords": 1
  }
}
```

**响应示例 (重置所有任务)**:

```json
{
  "ok": true,
  "message": "所有任务完成状态已重置",
  "data": {
    "userId": 123,
    "walletId": 456,
    "taskId": "all",
    "deletedRecords": 5
  }
}
```

**错误响应**:

```json
{
  "ok": false,
  "message": "服务器内部错误"
}
```

## 使用场景

该接口主要用于以下测试场景：

1. **测试任务完成流程**：当需要重复测试任务完成流程时，可以使用此接口重置任务状态，无需创建新用户。

2. **测试任务奖励机制**：重置任务完成状态后，可以再次完成任务并获得奖励，用于验证奖励发放机制。

3. **测试任务UI展示**：重置后可以测试任务从未完成到完成状态的UI变化和动画效果。

4. **测试任务依赖关系**：如果任务之间存在依赖关系，可以重置特定任务来测试依赖逻辑。

## 注意事项

- 此接口仅在测试环境中可用，生产环境中不应启用。
- 重置任务完成状态后，用户可以再次完成相同的任务并获得奖励。
- 此操作会删除UserTaskComplete表中的记录，但不会影响用户已经获得的奖励。
- 如果只需重置特定任务，请提供taskId参数；如需重置所有任务，则无需提供任何参数。