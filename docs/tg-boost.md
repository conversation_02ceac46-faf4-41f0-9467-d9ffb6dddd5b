# Telegram分享助力接口 API 文档

## 接口概述

**接口路径**：`POST /api/telegram/boost`

**接口描述**：该接口用于Telegram用户使用分享助力链接，为分享链接的创建者提供宝箱倒计时加速和宝石奖励。与普通的分享助力接口不同，此接口只需要Telegram认证，不需要钱包认证，因此未注册的Telegram用户也可以使用。

**请求方法**：POST

**认证要求**：需要Telegram认证（telegramAuthMiddleware）

## 认证方式

该接口使用Telegram WebApp的认证机制，前端需要在请求体中包含Telegram WebApp的initData。

```javascript
// 前端示例代码
const initData = window.Telegram.WebApp.initData;

fetch('/api/telegram/boost', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    initData: initData,
    code: 'abc123' // 分享链接的唯一码
  })
});
```

## 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--|--|--|--|
| initData | String | 是 | Telegram WebApp的初始化数据，用于验证用户身份 |
| code | String | 是 | 分享链接的唯一码，用于识别要使用的分享链接 |

## 响应结果

### 成功响应

```json
{
  "ok": true,
  "data": {
    "boostMinutes": 30,
    "gemAmount": 50,
  }
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
|--|--|--|
| boostMinutes | Number | 为分享链接创建者提供的宝箱倒计时加速时间（分钟） |
| gemAmount | Number | 为分享链接创建者提供的宝石奖励数量 |

### 错误响应

```json
{
  "ok": false,
  "message": "错误信息"
}
```

### 可能的错误信息

| 错误码 | 错误信息 | 描述 |
|--|--|--|
| 400 | errors.missingShareCode | 缺少分享码参数 |
| 400 | errors.shareLinkNotFound | 分享链接不存在 |
| 400 | errors.shareLinkExpired | 分享链接已过期 |
| 400 | errors.shareLinkMaxUsesReached | 分享链接已达到最大使用次数 |
| 400 | errors.sourceUserNotFound | 分享链接的创建者不存在 |
| 400 | errors.cannotBoostYourself | 不能给自己助力 |
| 400 | errors.alreadyUsedShareLink | 已经使用过该分享链接 |
| 401 | errors.missingInitData | 缺少Telegram初始化数据 |
| 401 | errors.invalidTelegramData | 无效的Telegram数据 |
| 401 | errors.userDataNotFound | 未找到用户数据 |
| 401 | errors.invalidTelegramUserId | 无效的Telegram用户ID |
| 401 | errors.authenticationFailed | 认证失败 |
| 500 | errors.serverError | 服务器内部错误 |

## 业务逻辑说明

1. **用户认证**：接口通过Telegram WebApp的initData验证用户身份，不需要钱包认证。

2. **用户类型**：
   - 已注册用户：系统会检查用户是否已经使用过该链接。
   - 未注册用户：系统会基于Telegram ID检查是否已经使用过该链接。

3. **奖励机制**：
   - 根据分享链接对应的宝箱等级提供不同的奖励：
     - 3级宝箱：提供基础的时间加速和宝石奖励
     - 4级宝箱：提供更高的时间加速和宝石奖励

4. **奖励发放**：
   - 为分享链接创建者的宝箱倒计时提供加速
   - 为分享链接创建者增加宝石奖励
   - 记录助力关系和钱包历史

## 注意事项

1. 每个Telegram用户只能使用同一个分享链接一次。
2. 分享链接有使用次数限制和过期时间。
3. 不能给自己助力。