# 批量资源更新 API 文档

## 概述

批量资源更新 API 是 Wolf Fun 游戏的核心资源管理接口，用于**带参数验证的智能资源更新**。该接口采用**1.5倍范围验证机制**，根据用户的 `lastActiveTime` 计算系统建议值，并验证用户请求是否合理，支持**5秒-2分钟**的在线用户高频请求，确保游戏经济平衡和防止异常请求。

### 🆕 最新更新（v3.0）

- **协调计算机制**：农场生产和出货线处理基于时间依赖关系进行协调计算，确保游戏机制的准确性
- **周期性计算**：基于离散时间间隔（5秒周期）计算资源，而非连续的每秒计算
- **自动初始化**：用户首次使用时自动创建默认的农场区块和出货线
- **BigNumber.js 精度**：所有数值计算使用 BigNumber.js 确保高精度，避免浮点数误差
- **实际变化追踪**：区分实际数据库变化量（`actual`）和理论计算值（`details`）
- **必须提供参数**：不再支持无参数调用，必须提供 `gemRequest` 或 `milkOperations` 参数

## 接口信息

- **路径**: `/api/wallet/batch-update-resources`
- **方法**: POST
- **描述**: 基于时间差和游戏机制动态计算资源，验证用户请求合理性，智能更新GEM和PendingMilk资源
- **认证**: 需要钱包认证，请求头中必须包含 `Authorization: Bearer <token>`
- **请求频率**: **5秒-2分钟**有效窗口，小于5秒或超过2分钟的请求将获得0资源

## 请求参数

### 请求头

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| Authorization | string | 是 | JWT Bearer token |
| Content-Type | string | 是 | application/json |
| Accept-Language | string | 否 | 语言设置 (zh/en，默认: zh) |

### 请求体

| 参数名 | 类型 | 必填 | 描述 | 限制 |
| ------ | ---- | ---- | ---- | ---- |
| gemRequest | number | 否 | 前端请求的GEM增量 | 必须为非负数（GEM只会增加不会减少），系统验证是否在1.5倍理论范围内 |
| milkOperations | object | 否 | 前端请求的牛奶操作 | 只支持对象形式（分别指定生产和消耗） |

**milkOperations 对象格式**：
| 字段名 | 类型 | 必填 | 描述 | 限制 |
| ------ | ---- | ---- | ---- | ---- |
| produce | number | 否 | 农场牛奶生产量 | 必须为非负数，表示农场生产的牛奶量 |
| consume | number | 否 | 出货线牛奶消耗量 | 必须为非负数，表示出货线消耗的牛奶量 |

**注意**:
- **必须提供参数**：必须提供 `gemRequest` 或 `milkOperations` 参数，不支持无参数调用
- **参数验证机制**：
  - `gemRequest`：必须为非负数，系统验证是否在1.5倍理论计算范围内
  - `milkOperations.produce`：必须为非负数，表示农场生产的牛奶量
  - `milkOperations.consume`：必须为非负数，表示出货线消耗的牛奶量
  - 如果提供 `milkOperations`，必须至少包含 `produce` 或 `consume` 字段之一
- **重要**：GEM只会增加不会减少，牛奶可以增加（produce）或减少（consume）
- **有效时间窗口：5秒-2分钟**
  - 小于5秒：防刷保护，获得0资源
  - 超过2分钟：判定为离线，获得0资源
  - 5秒-2分钟：正常在线用户，验证请求合理性并更新资源
- **协调计算机制**：农场生产和出货线处理基于时间依赖关系协调，出货线需要等待农场生产足够的牛奶

### 请求示例

**完整请求示例**（同时请求GEM和牛奶操作）：
```bash
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "gemRequest": 100.000,
    "milkOperations": {
      "produce": 50.000,
      "consume": 30.000
    }
  }'
```

**只请求GEM增量**：
```bash
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "gemRequest": 25.000
  }'
```

**只进行牛奶生产**：
```bash
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "milkOperations": {
      "produce": 50.000
    }
  }'
```

**只进行牛奶消耗**：
```bash
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "milkOperations": {
      "consume": 30.000
    }
  }'
```

**农场生产和出货线消耗组合**：
```bash
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "milkOperations": {
      "produce": 50.000,
      "consume": 30.000
    }
  }'
```

## 响应格式

### 成功响应 (状态码: 200)

**合理请求响应**（前端增量验证通过）：
```json
{
  "ok": true,
  "data": {
    "beforeUpdate": {
      "gem": 167491.000,
      "pendingMilk": 4000.000,
      "lastActiveTime": "2025-06-21 11:27:02"
    },
    "afterUpdate": {
      "gem": 167591.000,
      "pendingMilk": 4100.000,
      "lastActiveTime": "2025-06-21 16:21:31"
    },
    "changes": {
      "productionRates": {
        "farmMilkPerCycle": 7.595,
        "deliveryBlockUnit": 655360,
        "deliveryBlockPrice": 655360,
        "timeElapsedSeconds": 6.393
      },
      "actual": {
        "gem": {
          "changed": 100.000
        },
        "milk": {
          "changed": 100.000
        }
      },
      "details": {
        "gem": {
          "increased": 13.8
        },
        "milk": {
          "increased": 63.0,
          "decreased": 48.0
        }
      }
    },
    "timestamp": "2025-06-21 16:21:31"
  },
  "message": "Resources updated successfully"
}
```

**不合理请求响应**（前端增量验证失败，使用系统调整值）：
```json
{
  "ok": true,
  "data": {
    "beforeUpdate": {
      "gem": 167491.000,
      "pendingMilk": 4000.000,
      "lastActiveTime": "2025-06-21 11:27:02"
    },
    "afterUpdate": {
      "gem": 167541.000,
      "pendingMilk": 4025.000,
      "lastActiveTime": "2025-06-21 16:21:31"
    },
    "changes": {
      "usedSystemCalculation": true,
      "systemCalculationReason": "GEM增长量 (10000.000) 超出最大允许值 (50.000)",
      "actual": {
        "gem": {
          "changed": 50.000
        },
        "milk": {
          "changed": 25.000
        }
      },
      "details": {
        "gem": {
          "increased": 50.000
        },
        "milk": {
          "increased": 25.000,
          "decreased": 0.000
        }
      }
    },
    "timestamp": "2025-06-21 16:21:31"
  },
  "message": "Resources updated successfully"
}
```

**高频请求响应**（5秒间隔，部分限制）：
```json
{
  "ok": true,
  "data": {
    "beforeUpdate": {
      "gem": 167591.000,
      "pendingMilk": 4100.000,
      "lastActiveTime": "2025-06-21 12:21:31"
    },
    "afterUpdate": {
      "gem": 167604.918,
      "pendingMilk": 4102.784,
      "lastActiveTime": "2025-06-21 16:21:44"
    },
    "changes": {
      "actual": {
        "gem": {
          "changed": 13.918
        },
        "milk": {
          "changed": 2.784
        }
      },
      "details": {
        "gem": {
          "increased": 13.918
        },
        "milk": {
          "increased": 2.784,
          "decreased": 0.000
        }
      }
    },
    "timestamp": "2025-06-21 16:21:44"
  },
  "message": "Resources updated successfully"
}
```

**防刷保护响应**（小于5秒请求）：
```json
{
  "ok": true,
  "data": {
    "beforeUpdate": {
      "gem": 167676.000,
      "pendingMilk": 4116.900,
      "lastActiveTime": "2025-06-21 12:22:51"
    },
    "afterUpdate": {
      "gem": 167676.000,
      "pendingMilk": 4116.900,
      "lastActiveTime": "2025-06-21 16:22:51"
    },
    "changes": {
      "actual": {
        "gem": {
          "changed": 0.000
        },
        "milk": {
          "changed": 0.000
        }
      },
      "details": {
        "gem": {
          "increased": 0
        },
        "milk": {
          "increased": 0,
          "decreased": 0
        }
      }
    },
    "timestamp": "2025-06-21 16:22:51"
  },
  "message": "Resources updated successfully"
}
```

**离线用户响应**（超过1分钟请求）：
```json
{
  "ok": true,
  "data": {
    "beforeUpdate": {
      "gem": 167676.000,
      "pendingMilk": 4116.900,
      "lastActiveTime": "2025-06-21 11:20:00"
    },
    "afterUpdate": {
      "gem": 167676.000,
      "pendingMilk": 4116.900,
      "lastActiveTime": "2025-06-21 16:22:51"
    },
    "changes": {
      "actual": {
        "gem": {
          "changed": 0.000
        },
        "milk": {
          "changed": 0.000
        }
      },
      "details": {
        "gem": {
          "increased": 0
        },
        "milk": {
          "increased": 0,
          "decreased": 0
        }
      }
    },
    "timestamp": "2025-06-21 16:22:51"
  },
  "message": "Resources updated successfully"
}
```

## 响应字段说明

### 主要字段

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| ok | boolean | 请求是否成功 |
| data | object | 响应数据 |
| message | string | 操作结果消息 |

### data 字段详情

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| beforeUpdate | object | 更新前的资源状态 |
| afterUpdate | object | 更新后的资源状态 |
| changes | object | 本次更新的变化详情 |
| gameState | object | 当前游戏状态分析 |
| timestamp | string | 操作时间戳（格式：YYYY-MM-DD HH:mm:ss） |

### beforeUpdate/afterUpdate 字段

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| gem | number | GEM数量（3位小数精度） |
| pendingMilk | number | 待处理牛奶数量（3位小数精度） |
| lastActiveTime | string | 最后活跃时间（格式：YYYY-MM-DD HH:mm:ss） |

### changes 字段

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| usedSystemCalculation | boolean | 是否因请求不合理而使用了系统计算值 |
| systemCalculationReason | string | 使用系统计算值的原因说明 |
| productionRates | object | 生产速率调试信息 |
| actual | object | **实际的数据库变化量**（新增字段） |
| details | object | 理论计算的游戏机制产出量 |

**productionRates 对象详情**：
| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| farmMilkPerCycle | number | 农场每个生产周期的牛奶产量（基础配置） |
| deliveryBlockUnit | number | 出货线每个方块消耗的牛奶数量（基础配置） |
| deliveryBlockPrice | number | 出货线每个方块的GEM价格（基础配置） |
| timeElapsedSeconds | number | 实际经过的秒数 |

**actual 对象详情**（新增字段）：
| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| gem.changed | number | 实际数据库中GEM的变化量（正数=增加，负数=减少） |
| milk.changed | number | 实际数据库中牛奶的变化量（正数=增加，负数=减少） |

**details 对象详情**：
| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| gem.increased | number | 理论上应该产出的GEM增加量（游戏机制计算） |
| milk.increased | number | 理论上应该产出的牛奶增加量（农场生产） |
| milk.decreased | number | 理论上应该消耗的牛奶减少量（出货线消耗） |

## 字段说明：actual vs details

### actual 字段（实际变化量）
- **用途**：显示数据库中实际发生的变化量
- **数据来源**：直接计算数据库更新前后的差值
- **特点**：
  - `gem.changed`：实际增加的GEM数量（总是非负数）
  - `milk.changed`：实际的牛奶变化量（正数=增加，负数=减少）
  - 反映用户账户的真实变化

### details 字段（理论计算量）
- **用途**：显示根据游戏机制理论上应该产出的量
- **数据来源**：基于时间间隔和游戏配置的理论计算
- **特点**：
  - `gem.increased`：理论上应该产出的GEM
  - `milk.increased`：理论上农场应该生产的牛奶
  - `milk.decreased`：理论上出货线应该消耗的牛奶
  - 用于调试和验证游戏机制

### 为什么会有差异？
1. **用户请求限制**：用户请求的资源可能小于理论计算值
2. **验证机制**：超出1.5倍范围的请求会被调整为系统计算值
3. **游戏平衡**：某些情况下会限制资源产出以维护游戏平衡
4. **协调计算**：农场生产和出货线消耗需要协调，可能影响最终产出

### 示例对比
```json
{
  "actual": {
    "gem": { "changed": 5.000 },      // 用户实际得到5个GEM
    "milk": { "changed": 1.000 }      // 用户实际增加1个牛奶
  },
  "details": {
    "gem": { "increased": 25 },       // 理论上应该得到25个GEM
    "milk": {
      "increased": 21,                // 理论上农场生产21个牛奶
      "decreased": 25                 // 理论上出货线消耗25个牛奶
    }
  }
}
```

## 错误响应

### 参数验证失败 (状态码: 400)

```json
{
  "ok": false,
  "message": "参数验证失败",
  "error": "必须提供 gemRequest 或 milkOperations 参数"
}
```

**其他参数验证错误示例**：
```json
{
  "ok": false,
  "message": "参数验证失败",
  "error": "参数验证失败: gemRequest 必须为非负数（GEM只会增加不会减少）"
}
```

```json
{
  "ok": false,
  "message": "参数验证失败",
  "error": "参数验证失败: milkOperations 必须是包含 produce/consume 字段的对象"
}
```

### 钱包不存在 (状态码: 404)

```json
{
  "ok": false,
  "message": "钱包不存在",
  "error": "用户钱包不存在"
}
```

### 权限不足 (状态码: 403)

```json
{
  "ok": false,
  "message": "未授权访问",
  "error": "权限不足"
}
```

### 服务器错误 (状态码: 500)

```json
{
  "ok": false,
  "message": "批量资源更新失败",
  "error": "内部服务器错误"
}
```

## 业务逻辑说明

### 1. 参数验证和范围检查

- 验证 `gemRequest` 和 `milkOperations` 为有效数值
- `gemRequest` 必须为非负数（GEM只会增加不会减少）
- `milkOperations.produce` 和 `milkOperations.consume` 必须为非负数
- 确保至少提供 `gemRequest` 或 `milkOperations` 参数之一
- 验证用户JWT token有效性

### 2. 用户状态验证

- 检查用户钱包是否存在
- 验证用户权限
- 获取当前游戏状态

### 3. 基于时间的协调计算机制

**核心机制**：
- 根据用户的 `lastActiveTime` 精确计算从上次更新到现在应该获得的资源
- **5秒防刷保护**：距离上次更新小于5秒的请求将获得0资源
- **2分钟离线判定**：距离上次更新超过2分钟的请求将获得0资源
- **有效时间窗口**：只有5秒-2分钟内的请求才能获得基于时间计算的资源

**协调计算逻辑**：
- **时间依赖关系**：农场生产和出货线处理基于时间事件队列进行协调
- **周期性计算**：基于5秒生产周期计算完整周期数，而非连续每秒计算
- **牛奶库存限制**：出货线必须等待农场生产足够的牛奶才能处理
- **事件队列处理**：按时间顺序处理农场生产事件和出货线处理事件

**计算公式**：
- **农场生产** = Σ(完整周期数 × 每周期产量) 对所有已解锁农场区块
- **出货线处理** = 完整周期数 × 每周期处理量（受牛奶库存限制）
- **GEM产出** = 出货线实际处理量 × 每单位GEM价格

**1.5倍范围验证机制**：
- 系统基于协调计算得出理论资源值
- 验证用户请求是否在1.5倍范围内
- 合理请求：按用户要求分配资源
- 不合理请求：使用系统计算值并标明原因

### 4. 自动初始化机制

- **农场区块自动创建**：用户首次使用时自动创建默认农场区块（1级，1牛奶/5秒）
- **出货线自动创建**：用户首次使用时自动创建默认出货线（1级，5牛奶/5秒，5GEM/方块）
- **确保数据完整性**：每个用户都有完整的游戏数据结构

### 5. 游戏状态分析

- 计算农场总产量（所有已解锁区块的周期产量之和）
- 计算出货线处理能力（基于处理周期和方块单位）
- 分析产能平衡状态，识别瓶颈
- 提供基础配置信息用于调试

### 6. 数据库操作

- 使用数据库事务确保数据一致性
- 更新用户钱包的 gem 字段（使用BigNumber.js字符串表示）
- 更新出货线的 pendingMilk 字段
- 创建详细的 WalletHistory 记录用于审计
- **统一更新 lastActiveTime 为当前时间**
- 支持自动创建缺失的农场区块和出货线记录

### 7. 高精度计算

- 使用 BigNumber.js 确保数值计算精度
- 所有返回值保持3位小数精度（使用 toFixed(3) 或 decimalPlaces(3)）
- 避免浮点数精度问题
- 数据库存储使用字符串格式保持精度

## 使用场景

### 1. 合理资源请求（在1.5倍范围内）

```bash
# 请求合理数量的资源，系统验证通过后按请求分配
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "gemRequest": 50.000,
    "milkOperations": {
      "produce": 25.000
    }
  }'
```

### 2. 牛奶消耗操作

```bash
# 消耗待处理牛奶，用于出货线处理
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "milkOperations": {
      "consume": 30.000
    }
  }'
```

### 3. 不合理请求处理

```bash
# 请求过大数量，系统自动使用计算值并说明原因
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "gemRequest": 10000.000,
    "milkOperations": {
      "produce": 5000.000
    }
  }'
```

## 时间机制详解

### 1. 时间窗口机制（5秒-2分钟）

**有效时间窗口**：
- 系统会检查距离上次更新的时间间隔
- **小于5秒**：防刷保护，资源上限设为0
- **5秒-2分钟**：正常在线用户，基于时间计算资源
- **超过2分钟**：离线用户，资源上限设为0
- 请求仍然成功，但只有在有效窗口内才能获得资源

**时间计算**：
```
时间差(小时) = (当前时间 - lastActiveTime) / 3600000毫秒
如果时间差 < 5/3600 小时，则上限 = 0 (防刷保护)
如果时间差 > 2/60 小时，则上限 = 0 (离线判定)
如果 5/3600 <= 时间差 <= 2/60，则正常计算资源上限
```

### 2. 动态资源计算

**GEM计算示例**：
```
出货线处理能力 = 1.0 单位/秒
经过时间 = 0.5 小时
GEM上限 = 1.0 × 0.5 × 3600 = 1800 GEM
```

**PendingMilk计算示例**：
```
农场总产能 = 0.2 牛奶/秒
经过时间 = 0.5 小时
PendingMilk上限 = 0.2 × 0.5 × 3600 = 360 牛奶
```

### 3. 实际应用场景

| 时间间隔 | 场景描述 | 资源获得 | 适用情况 | 状态 |
| -------- | -------- | -------- | -------- | ---- |
| < 5秒 | 快速连续请求 | 0资源 | 防刷保护 | 在线 |
| 5秒-2分钟 | 实时游戏操作 | 基于时间计算 | 正常游戏 | 在线 |
| > 2分钟 | 离线用户 | 0资源 | 离线判定 | 离线 |

**注意**：只有在5秒-2分钟的时间窗口内，用户才能获得基于时间计算的资源。

## 注意事项

### 1. 数据精度

- 所有数值计算使用 BigNumber.js 确保精度
- 输入和输出值均保持3位小数精度
- 避免 JavaScript 原生数值计算的精度问题

### 2. 基于时间的动态资源机制

**核心特性**：
- **5秒防刷保护**：小于5秒的请求将获得0资源，有效防止刷取
- **2分钟离线判定**：超过2分钟的请求将获得0资源，判定为离线用户
- **有效时间窗口**：只有5秒-2分钟内的请求才能获得资源
- **动态时间计算**：根据 `lastActiveTime` 精确计算应得资源
- **实时游戏支持**：支持在线用户的高频请求，适合实时游戏体验

**协调计算公式**：
- **农场生产** = Σ(Math.floor(时间秒数 / 生产周期) × 每周期产量) 对所有农场区块
- **出货线处理** = Math.floor(时间秒数 / 处理周期) × 每周期处理量（受牛奶库存限制）
- **GEM产出** = 出货线实际处理量 × 每单位GEM价格

**协调计算示例**：
- 经过30秒，农场5秒/周期，1牛奶/周期 → 农场生产 = Math.floor(30/5) × 1 = 6牛奶
- 经过30秒，出货线5秒/周期，5牛奶/方块，5GEM/方块，初始牛奶0 → 出货线处理 = Math.floor(30/5) = 6周期，但受牛奶限制实际处理1方块 = 5GEM
- 经过3秒 → 所有资源上限 = 0（防刷保护）
- 经过2分钟 → 所有资源上限 = 0（离线判定）

**游戏经济平衡**：
- 防止无限制刷取资源
- 确保资源增长与实际游戏进度和时间相匹配
- 基于真实的游戏产能计算，维护游戏经济平衡

### 3. 用户活跃状态

- 每次成功调用都会更新用户的 lastActiveTime
- 确保用户被正确标记为在线状态
- 影响离线奖励的计算

### 4. 审计追踪

- 所有资源变更都会创建 WalletHistory 记录
- 支持完整的审计追踪
- 记录操作时间、数量、类型等详细信息

### 5. 错误处理

- 提供详细的错误信息和错误码
- 支持多语言错误消息
- 所有错误都会记录到系统日志

### 6. 性能考虑

- 使用数据库事务确保数据一致性
- 单次请求处理多个资源更新，减少数据库访问
- 智能的游戏状态分析，提供有价值的反馈信息
- 支持高频请求（5秒间隔），适合实时游戏需求

## API测试示例

### 1. 测试时间窗口机制

```bash
# 第一次请求（正常，在有效窗口内）
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"

# 立即第二次请求（防刷保护，小于5秒）
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"

# 等待6秒后第三次请求（正常，在有效窗口内）
sleep 6
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"

# 等待2分钟后第四次请求（离线判定，超过1分钟）
sleep 120
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### 2. 测试离线用户

```bash
# 模拟离线用户的资源请求（超过1分钟）
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### 3. 预期响应分析

**5秒内请求（防刷保护）**：
- `details.gem.increased`: 0
- `details.milk.increased`: 0
- `details.milk.decreased`: 0

**5秒-2分钟请求（正常在线用户）**：
- 合理请求：按用户请求数量分配
- 不合理请求：使用系统计算值，并标明 `usedSystemCalculation: true`

**超过2分钟请求（离线用户）**：
- `details.gem.increased`: 0
- `details.milk.increased`: 0
- `details.milk.decreased`: 0

**1.5倍范围验证**：
- `gemRequest` 范围：`[0, 系统协调计算GEM产出 × 1.5]`
- `milkOperations.produce` 范围：`[0, 农场协调计算生产量 × 1.5]`
- `milkOperations.consume` 范围：`[0, 出货线协调计算消耗量 × 1.5]`

## 技术实现细节

### 1. 协调计算算法

该接口使用事件队列算法来模拟农场生产和出货线处理的时间依赖关系：

```javascript
// 伪代码示例
const events = [];

// 添加农场生产事件
farmPlots.forEach(plot => {
  let time = plot.productionSpeed;
  while (time <= timeElapsed) {
    events.push({ time, type: 'farm', amount: plot.milkProduction });
    time += plot.productionSpeed;
  }
});

// 添加出货线处理事件
let time = deliveryLine.deliverySpeed;
while (time <= timeElapsed) {
  events.push({ time, type: 'delivery', amount: deliveryLine.blockUnit });
  time += deliveryLine.deliverySpeed;
}

// 按时间排序并处理事件
events.sort((a, b) => a.time - b.time);
let pendingMilk = initialPendingMilk;
let totalGemProduced = 0;

events.forEach(event => {
  if (event.type === 'farm') {
    pendingMilk += event.amount;
  } else if (event.type === 'delivery' && pendingMilk >= event.amount) {
    pendingMilk -= event.amount;
    totalGemProduced += deliveryLine.blockPrice;
  }
});
```

### 2. BigNumber.js 精度处理

所有数值计算使用 BigNumber.js 确保精度：

```javascript
// 示例：GEM更新
const currentGemBN = createBigNumber(wallet.gem || 0);
const gemIncrementBN = createBigNumber(gemAmount);
const newGemBN = currentGemBN.plus(gemIncrementBN);
wallet.gem = newGemBN.toFixed(3); // 保持3位小数精度
```

### 3. 自动初始化逻辑

用户首次使用时自动创建默认配置：

```javascript
// 默认农场区块配置
const defaultFarmPlot = {
  plotNumber: 1,
  level: 1,
  isUnlocked: true,
  milkProduction: 1,    // 1牛奶/次
  productionSpeed: 5,   // 5秒/次
  barnCount: 1,
  upgradeCost: 100
};

// 默认出货线配置
const defaultDeliveryLine = {
  level: 1,
  deliverySpeed: 1,     // 1秒/次
  blockUnit: 5,         // 5牛奶/方块
  blockPrice: 5,        // 5GEM/方块
  upgradeCost: 500
};
```

## 最佳实践

### 1. 前端集成建议

- **定时请求**：建议每10-30秒调用一次，避免过于频繁的请求
- **参数计算**：前端应基于游戏状态计算合理的资源请求量
- **错误处理**：妥善处理验证失败的情况，使用系统计算值
- **精度处理**：前端显示时使用3位小数格式

### 2. 游戏平衡考虑

- **时间窗口**：5秒-2分钟的时间窗口确保游戏节奏合理
- **协调机制**：农场和出货线的协调确保游戏机制的真实性
- **验证范围**：1.5倍验证范围允许合理的网络延迟和计算误差

### 3. 监控和调试

- **actual vs details**：通过对比实际变化和理论计算识别问题
- **productionRates**：基础配置信息帮助调试游戏机制
- **systemCalculationReason**：详细的原因说明帮助理解验证逻辑

### 4. 性能优化

- **事务使用**：确保数据一致性的同时最小化锁定时间
- **批量操作**：单次请求处理多种资源更新
- **索引优化**：确保 walletId 和 userId 字段有适当的数据库索引
