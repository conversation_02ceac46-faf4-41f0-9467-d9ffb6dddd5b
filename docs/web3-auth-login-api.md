# Web3钱包登录接口

## 接口信息

- **路径**: `/api/web3-auth/login`
- **方法**: POST
- **认证要求**: 无需认证

## 请求参数

### 请求体 (JSON)

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| walletAddress | string | 是 | 用户的钱包地址 |
| signature | string | 是 | 签名数据 |
| message | string | 是 | 签名消息（从/api/web3-auth/nonce接口获取） |
| referralCode | string | 否 | 推荐码 |

### 请求体示例

```json
{
  "walletAddress": "0x1234...5678",
  "signature": "0xabcd...efgh",
  "message": "Sign this message for authentication: 123e4567-e89b-12d3-a456-426614174000",
  "referralCode": "USER123"
}
```

## 响应结果

### 成功响应

- **状态码**: 200 OK
- **响应体**: 

```json
{
  "ok": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIs...",
    "user": {
      "id": 12,
      "username": "wallet_0xa1ca25",
      "walletAddress": "******************************************"
    }
  }
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| token | string | JWT认证令牌 |
| user.id | number | 用户ID |
| user.username | string | 用户名 |
| user.walletAddress | string | 用户钱包地址 |

### 错误响应

#### 参数验证失败

- **状态码**: 400 Bad Request
- **响应体**:

```json
{
  "ok": false,
  "data": ["参数验证错误详情"]
}
```

#### Nonce无效

- **状态码**: 400 Bad Request
- **响应体**:

```json
{
  "ok": false,
  "data": "无效的nonce"
}
```

#### 登录失败

- **状态码**: 400 Bad Request
- **响应体**:

```json
{
  "ok": false,
  "data": "登录失败：具体错误信息"
}
```

## 注意事项

1. 在调用此接口前，需要先调用 `/api/web3-auth/nonce` 接口获取签名消息
2. 签名消息的有效期为5分钟
3. 登录成功后返回的token需要在后续请求中通过Authorization头部传递，格式为：`Bearer {token}`