# 一次性领取4个宝箱 API 文档

本文档描述了一次性领取4个宝箱的API接口。该接口允许用户一次性获取4个宝箱，但每个用户只能使用一次。

## 基本信息

- 基础路径: `/api/jackpot-chest`
- 接口需要用户身份验证（钱包认证）

## API 接口详情

### 一次性领取4个宝箱

该接口允许用户一次性领取4个宝箱，每个用户只能使用一次。系统会检查用户是否已经领取过这些宝箱，如果已经领取过，则会返回错误信息。

**请求方法**: POST

**路径**: `/api/jackpot-chest/collect-four-chests`

**请求头**:

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| Authorization | String | 是 | 用户认证令牌，格式为 `Bearer {token}` |

**请求参数**: 无需额外参数

**响应示例**:

成功响应:
```json
{
  "ok": true,
  "data": {
    "result": {
      "openedCount": 4,
      "chestIds": [40, 41, 42, 43],
      "rewards": [
        {
          "level": 1,
          "items": [
            {
              "type": "fragment_green",
              "amount": 18
            },
            {
              "type": "gem",
              "amount": 4376
            }
          ]
        },
        {
          "level": 1,
          "items": [
            {
              "type": "fragment_green",
              "amount": 24
            },
            {
              "type": "gem",
              "amount": 4363
            }
          ]
        },
        {
          "level": 1,
          "items": [
            {
              "type": "fragment_green",
              "amount": 18
            },
            {
              "type": "gem",
              "amount": 4996
            }
          ]
        },
        {
          "level": 1,
          "items": [
            {
              "type": "fragment_green",
              "amount": 21
            },
            {
              "type": "gem",
              "amount": 1356
            }
          ]
        }
      ],
      "summary": {
        "ticket": 0,
        "fragment_green": 81,
        "fragment_blue": 0,
        "fragment_purple": 0,
        "fragment_gold": 0,
        "ton": 0,
        "gem": 15091
      },
      "levelSummary": {
        "level1": 4,
        "level2": 0,
        "level3": 0,
        "level4": 0
      },
      "shareLinks": [],
      "jackpotWinner": null
    }
  }
}
```

失败响应 (已领取过):
```json
{
  "ok": false,
  "message": "您已经领取过一次性4个宝箱"
}
```

## 错误码说明

| HTTP状态码 | 错误信息 | 描述 |
| --- | --- | --- |
| 400 | 您已经领取过一次性4个宝箱 | 用户已经领取过这4个宝箱，不能重复领取 |
| 401 | 未授权 | 用户未登录或认证失败 |
| 500 | 服务器错误 | 服务器内部错误 |

## 注意事项

1. 每个用户只能使用此接口一次，重复调用将返回错误
2. 宝箱奖励是随机生成的，可能包括宝石、TON代币、门票等
3. 所有宝箱会在领取后自动打开，无需额外调用开启宝箱接口